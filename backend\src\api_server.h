#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>

#include <crow.h>
#include <jwt-cpp/jwt.h>
#include <json/json.h>

#include "../../core/engine/trading_engine.h"
#include "database_manager.h"

struct ApiConfig {
    std::string host = "0.0.0.0";
    int port = 8080;
    int thread_count = 4;
    bool enable_cors = true;
    bool enable_ssl = false;
    std::string ssl_cert_file;
    std::string ssl_key_file;
    std::string jwt_secret = "zenflow_secret_key_change_in_production";
    int jwt_expiry_hours = 24;
    bool enable_rate_limiting = true;
    int rate_limit_requests_per_minute = 100;
};

struct UserSession {
    std::string user_id;
    std::string username;
    std::string email;
    std::vector<std::string> permissions;
    int64_t created_at;
    int64_t last_activity;
    bool is_admin = false;
};

class ApiServer {
public:
    ApiServer(const ApiConfig& config, 
              zenflow::TradingEngine* trading_engine,
              DatabaseManager* database_manager);
    ~ApiServer();

    bool Initialize();
    bool Start();
    void Stop();
    bool IsRunning() const { return is_running_.load(); }

private:
    // Configuration
    ApiConfig config_;
    
    // Dependencies
    zenflow::TradingEngine* trading_engine_;
    DatabaseManager* database_manager_;
    
    // Crow application
    std::unique_ptr<crow::SimpleApp> app_;
    std::thread server_thread_;
    std::atomic<bool> is_running_{false};
    
    // Session management
    std::unordered_map<std::string, UserSession> active_sessions_;
    std::mutex sessions_mutex_;
    
    // Rate limiting
    std::unordered_map<std::string, std::vector<int64_t>> rate_limit_map_;
    std::mutex rate_limit_mutex_;
    
    // Setup methods
    void SetupMiddleware();
    void SetupRoutes();
    void SetupCORS();
    void SetupAuthentication();
    void SetupRateLimiting();
    
    // Authentication endpoints
    void SetupAuthRoutes();
    crow::response HandleLogin(const crow::request& req);
    crow::response HandleLogout(const crow::request& req);
    crow::response HandleRegister(const crow::request& req);
    crow::response HandleRefreshToken(const crow::request& req);
    crow::response HandleProfile(const crow::request& req);
    
    // Market data endpoints
    void SetupMarketDataRoutes();
    crow::response HandleGetQuote(const crow::request& req, const std::string& symbol);
    crow::response HandleGetQuotes(const crow::request& req);
    crow::response HandleGetHistoricalData(const crow::request& req, const std::string& symbol);
    crow::response HandleGetCandles(const crow::request& req, const std::string& symbol);
    crow::response HandleSearchSymbols(const crow::request& req);
    
    // Trading endpoints
    void SetupTradingRoutes();
    crow::response HandlePlaceOrder(const crow::request& req);
    crow::response HandleCancelOrder(const crow::request& req, const std::string& order_id);
    crow::response HandleModifyOrder(const crow::request& req, const std::string& order_id);
    crow::response HandleGetOrders(const crow::request& req);
    crow::response HandleGetOrder(const crow::request& req, const std::string& order_id);
    crow::response HandleGetPositions(const crow::request& req);
    crow::response HandleGetPosition(const crow::request& req, const std::string& symbol);
    crow::response HandleClosePosition(const crow::request& req, const std::string& symbol);
    
    // Portfolio endpoints
    void SetupPortfolioRoutes();
    crow::response HandleGetPortfolio(const crow::request& req);
    crow::response HandleGetPerformance(const crow::request& req);
    crow::response HandleGetTransactions(const crow::request& req);
    crow::response HandleGetPnL(const crow::request& req);
    
    // Strategy endpoints
    void SetupStrategyRoutes();
    crow::response HandleGetStrategies(const crow::request& req);
    crow::response HandleCreateStrategy(const crow::request& req);
    crow::response HandleUpdateStrategy(const crow::request& req, const std::string& strategy_id);
    crow::response HandleDeleteStrategy(const crow::request& req, const std::string& strategy_id);
    crow::response HandleStartStrategy(const crow::request& req, const std::string& strategy_id);
    crow::response HandleStopStrategy(const crow::request& req, const std::string& strategy_id);
    crow::response HandleBacktestStrategy(const crow::request& req, const std::string& strategy_id);
    
    // Risk management endpoints
    void SetupRiskRoutes();
    crow::response HandleGetRiskMetrics(const crow::request& req);
    crow::response HandleSetRiskLimits(const crow::request& req);
    crow::response HandleGetRiskLimits(const crow::request& req);
    crow::response HandleEmergencyStop(const crow::request& req);
    
    // Admin endpoints
    void SetupAdminRoutes();
    crow::response HandleGetUsers(const crow::request& req);
    crow::response HandleGetSystemStatus(const crow::request& req);
    crow::response HandleGetLogs(const crow::request& req);
    crow::response HandleSystemConfig(const crow::request& req);
    
    // Utility methods
    bool AuthenticateRequest(const crow::request& req, UserSession& session);
    bool CheckRateLimit(const std::string& client_ip);
    std::string GenerateJWT(const UserSession& session);
    bool ValidateJWT(const std::string& token, UserSession& session);
    crow::response CreateErrorResponse(int code, const std::string& message);
    crow::response CreateSuccessResponse(const Json::Value& data);
    Json::Value ParseRequestBody(const crow::request& req);
    bool HasPermission(const UserSession& session, const std::string& permission);
    
    // Data conversion helpers
    Json::Value OrderToJson(const zenflow::Order& order);
    Json::Value PositionToJson(const zenflow::Position& position);
    Json::Value MarketDataToJson(const zenflow::MarketData& data);
    Json::Value CandleToJson(const zenflow::Candle& candle);
    zenflow::OrderRequest JsonToOrderRequest(const Json::Value& json);
    
    // Session management
    void CleanupExpiredSessions();
    void UpdateSessionActivity(const std::string& session_id);
    
    // Logging helpers
    void LogRequest(const crow::request& req, const crow::response& res);
    void LogError(const std::string& message, const crow::request& req);
};

// Middleware classes
class CORSMiddleware {
public:
    struct context {};
    
    void before_handle(crow::request& req, crow::response& res, context& ctx);
    void after_handle(crow::request& req, crow::response& res, context& ctx);
};

class AuthMiddleware {
public:
    struct context {
        UserSession session;
        bool authenticated = false;
    };
    
    AuthMiddleware(ApiServer* server) : server_(server) {}
    
    void before_handle(crow::request& req, crow::response& res, context& ctx);
    void after_handle(crow::request& req, crow::response& res, context& ctx);

private:
    ApiServer* server_;
};

class RateLimitMiddleware {
public:
    struct context {};
    
    RateLimitMiddleware(ApiServer* server) : server_(server) {}
    
    void before_handle(crow::request& req, crow::response& res, context& ctx);
    void after_handle(crow::request& req, crow::response& res, context& ctx);

private:
    ApiServer* server_;
};

#endif // API_SERVER_H
