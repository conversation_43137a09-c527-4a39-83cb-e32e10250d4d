#include "market_data_handler.h"
#include <algorithm>
#include <stdexcept>
#include <sstream>
#include <iomanip>
#include <curl/curl.h>
#include <json/json.h>

namespace zenflow {

// HTTP response callback for libcurl
static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
    userp->append((char*)contents, size * nmemb);
    return size * nmemb;
}

MarketDataHandler::MarketDataHandler() {
    curl_global_init(CURL_GLOBAL_DEFAULT);
}

MarketDataHandler::~MarketDataHandler() {
    if (is_running_.load()) {
        Stop();
    }
    curl_global_cleanup();
}

bool MarketDataHandler::Initialize() {
    std::lock_guard<std::mutex> lock(providers_mutex_);
    
    if (is_initialized_.load()) {
        return true;
    }

    try {
        // Initialize all providers
        for (auto& provider : providers_) {
            if (!provider->Initialize()) {
                return false;
            }
        }

        is_initialized_.store(true);
        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool MarketDataHandler::Start() {
    std::lock_guard<std::mutex> lock(providers_mutex_);
    
    if (!is_initialized_.load()) {
        return false;
    }

    if (is_running_.load()) {
        return true;
    }

    try {
        // Start all providers
        for (auto& provider : providers_) {
            provider->SetDataCallback([this](const MarketData& data) {
                OnDataReceived(data);
            });
            
            if (!provider->Start()) {
                return false;
            }
        }

        is_running_.store(true);
        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool MarketDataHandler::Stop() {
    std::lock_guard<std::mutex> lock(providers_mutex_);
    
    if (!is_running_.load()) {
        return true;
    }

    try {
        // Stop all providers
        for (auto& provider : providers_) {
            provider->Stop();
        }

        is_running_.store(false);
        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool MarketDataHandler::AddProvider(std::unique_ptr<MarketDataProvider> provider) {
    std::lock_guard<std::mutex> lock(providers_mutex_);
    
    if (!provider) {
        return false;
    }

    providers_.push_back(std::move(provider));
    return true;
}

bool MarketDataHandler::Subscribe(const std::string& symbol, const std::string& provider_name) {
    std::lock_guard<std::mutex> lock(providers_mutex_);
    
    bool success = false;
    
    for (auto& provider : providers_) {
        if (provider_name.empty() || provider->GetProviderName() == provider_name) {
            if (provider->Subscribe(symbol)) {
                success = true;
            }
        }
    }
    
    return success;
}

bool MarketDataHandler::Unsubscribe(const std::string& symbol, const std::string& provider_name) {
    std::lock_guard<std::mutex> lock(providers_mutex_);
    
    bool success = false;
    
    for (auto& provider : providers_) {
        if (provider_name.empty() || provider->GetProviderName() == provider_name) {
            if (provider->Unsubscribe(symbol)) {
                success = true;
            }
        }
    }
    
    return success;
}

MarketData MarketDataHandler::GetLatestData(const std::string& symbol) const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    
    auto it = latest_data_.find(symbol);
    if (it != latest_data_.end()) {
        return it->second;
    }
    
    return MarketData{}; // Return empty data if not found
}

void MarketDataHandler::OnDataReceived(const MarketData& data) {
    // Update latest data
    {
        std::lock_guard<std::mutex> lock(data_mutex_);
        latest_data_[data.symbol] = data;
        
        // Store in historical data (keep last 1000 ticks per symbol)
        auto& hist = historical_data_[data.symbol];
        hist.push(data);
        if (hist.size() > 1000) {
            hist.pop();
        }
    }

    // Update candles
    UpdateCandles(data);
    
    // Update statistics
    UpdateStatistics(data);
    
    // Call user callback
    if (data_callback_) {
        data_callback_(data);
    }
}

void MarketDataHandler::UpdateCandles(const MarketData& data) {
    std::lock_guard<std::mutex> lock(candles_mutex_);
    
    std::vector<std::string> timeframes = {"1m", "5m", "15m", "1h", "4h", "1d"};
    
    for (const auto& timeframe : timeframes) {
        int64_t aligned_time = AlignTimestamp(data.timestamp, timeframe);
        
        auto& candle = current_candles_[data.symbol][timeframe];
        
        if (candle.timestamp != aligned_time) {
            // New candle period
            if (candle.timestamp != 0 && candle_callback_) {
                candle_callback_(candle);
            }
            
            // Initialize new candle
            candle.symbol = data.symbol;
            candle.timestamp = aligned_time;
            candle.timeframe = timeframe;
            candle.open = data.price;
            candle.high = data.price;
            candle.low = data.price;
            candle.close = data.price;
            candle.volume = data.volume;
        } else {
            // Update existing candle
            candle.high = std::max(candle.high, data.price);
            candle.low = std::min(candle.low, data.price);
            candle.close = data.price;
            candle.volume += data.volume;
        }
    }
}

void MarketDataHandler::UpdateStatistics(const MarketData& data) {
    total_ticks_received_.fetch_add(1);
    
    std::lock_guard<std::mutex> lock(stats_mutex_);
    symbol_tick_counts_[data.symbol]++;
}

int64_t MarketDataHandler::GetCurrentTimestamp() const {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

int64_t MarketDataHandler::AlignTimestamp(int64_t timestamp, const std::string& timeframe) const {
    int64_t interval_ms = 0;
    
    if (timeframe == "1m") interval_ms = 60 * 1000;
    else if (timeframe == "5m") interval_ms = 5 * 60 * 1000;
    else if (timeframe == "15m") interval_ms = 15 * 60 * 1000;
    else if (timeframe == "1h") interval_ms = 60 * 60 * 1000;
    else if (timeframe == "4h") interval_ms = 4 * 60 * 60 * 1000;
    else if (timeframe == "1d") interval_ms = 24 * 60 * 60 * 1000;
    else return timestamp;
    
    return (timestamp / interval_ms) * interval_ms;
}

std::string MarketDataHandler::HttpGet(const std::string& url) const {
    CURL* curl;
    CURLcode res;
    std::string response;

    curl = curl_easy_init();
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        
        res = curl_easy_perform(curl);
        curl_easy_cleanup(curl);
        
        if (res != CURLE_OK) {
            return "";
        }
    }
    
    return response;
}

// Alpha Vantage Provider Implementation
AlphaVantageProvider::AlphaVantageProvider(const std::string& api_key) 
    : api_key_(api_key) {}

bool AlphaVantageProvider::Initialize() {
    return !api_key_.empty();
}

bool AlphaVantageProvider::Start() {
    if (is_running_.load()) {
        return true;
    }
    
    is_running_.store(true);
    polling_thread_ = std::thread(&AlphaVantageProvider::PollData, this);
    return true;
}

bool AlphaVantageProvider::Stop() {
    is_running_.store(false);
    if (polling_thread_.joinable()) {
        polling_thread_.join();
    }
    return true;
}

bool AlphaVantageProvider::Subscribe(const std::string& symbol) {
    std::lock_guard<std::mutex> lock(symbols_mutex_);
    
    auto it = std::find(subscribed_symbols_.begin(), subscribed_symbols_.end(), symbol);
    if (it == subscribed_symbols_.end()) {
        subscribed_symbols_.push_back(symbol);
        return true;
    }
    return false;
}

void AlphaVantageProvider::PollData() {
    while (is_running_.load()) {
        std::vector<std::string> symbols;
        {
            std::lock_guard<std::mutex> lock(symbols_mutex_);
            symbols = subscribed_symbols_;
        }
        
        for (const auto& symbol : symbols) {
            if (!is_running_.load()) break;
            
            std::string url = "https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=" 
                            + symbol + "&apikey=" + api_key_;
            
            std::string response = ""; // HttpGet would be implemented here
            if (!response.empty() && data_callback_) {
                MarketData data = ParseQuoteData(response, symbol);
                if (!data.symbol.empty()) {
                    data_callback_(data);
                }
            }
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1)); // Rate limiting
    }
}

MarketData AlphaVantageProvider::ParseQuoteData(const std::string& json_response, const std::string& symbol) {
    MarketData data;
    data.symbol = symbol;
    data.type = MarketDataType::QUOTE;
    data.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    // JSON parsing would be implemented here
    // For now, return mock data
    data.price = 100.0 + (rand() % 1000) / 100.0;
    data.bid = data.price - 0.01;
    data.ask = data.price + 0.01;
    data.volume = 1000 + (rand() % 5000);
    
    return data;
}

} // namespace zenflow
