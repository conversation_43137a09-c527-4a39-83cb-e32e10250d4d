#pragma once

#include <QMainWindow>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QDockWidget>
#include <QSplitter>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QLineEdit>
#include <QTableWidget>
#include <QTreeWidget>
#include <QTextEdit>
#include <QProgressBar>
#include <QTimer>
#include <QSystemTrayIcon>
#include <QCloseEvent>
#include <memory>

#include "../../core/engine/trading_engine.h"

// Forward declarations
class TradingWidget;
class ChartWidget;
class PortfolioWidget;
class OrderBookWidget;
class WatchlistWidget;
class NewsWidget;
class LogWidget;
class StrategyWidget;
class RiskWidget;
class PerformanceWidget;

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;
    void changeEvent(QEvent *event) override;

private slots:
    // File menu
    void OnNewWorkspace();
    void OnOpenWorkspace();
    void OnSaveWorkspace();
    void OnExit();
    
    // Edit menu
    void OnPreferences();
    
    // View menu
    void OnToggleFullScreen();
    void OnResetLayout();
    void OnToggleDockWidget();
    
    // Trading menu
    void OnQuickBuy();
    void OnQuickSell();
    void OnCloseAllPositions();
    void OnCancelAllOrders();
    
    // Tools menu
    void OnStrategyBuilder();
    void OnBacktester();
    void OnRiskAnalyzer();
    void OnMarketScanner();
    
    // Help menu
    void OnAbout();
    void OnUserGuide();
    void OnKeyboardShortcuts();
    void OnCheckUpdates();
    
    // System tray
    void OnTrayIconActivated(QSystemTrayIcon::ActivationReason reason);
    void OnShowHide();
    
    // Status updates
    void UpdateConnectionStatus();
    void UpdateMarketStatus();
    void UpdateAccountInfo();
    void UpdatePerformanceMetrics();
    
    // Trading engine events
    void OnOrderEvent(const zenflow::OrderEvent& event);
    void OnPositionEvent(const zenflow::PositionEvent& event);
    void OnRiskEvent(const zenflow::RiskEvent& event);

private:
    // UI setup
    void SetupUI();
    void CreateMenuBar();
    void CreateToolBars();
    void CreateStatusBar();
    void CreateDockWidgets();
    void CreateCentralWidget();
    void SetupSystemTray();
    void LoadSettings();
    void SaveSettings();
    void SetupConnections();
    void ApplyTheme();
    
    // Menu bar
    QMenuBar* menu_bar_;
    QMenu* file_menu_;
    QMenu* edit_menu_;
    QMenu* view_menu_;
    QMenu* trading_menu_;
    QMenu* tools_menu_;
    QMenu* help_menu_;
    
    // Tool bars
    QToolBar* main_toolbar_;
    QToolBar* trading_toolbar_;
    QToolBar* chart_toolbar_;
    
    // Status bar
    QStatusBar* status_bar_;
    QLabel* connection_status_label_;
    QLabel* market_status_label_;
    QLabel* account_balance_label_;
    QLabel* pnl_label_;
    QProgressBar* cpu_usage_bar_;
    QProgressBar* memory_usage_bar_;
    
    // Central widget
    QTabWidget* central_tabs_;
    
    // Dock widgets
    QDockWidget* watchlist_dock_;
    QDockWidget* trading_dock_;
    QDockWidget* portfolio_dock_;
    QDockWidget* orders_dock_;
    QDockWidget* positions_dock_;
    QDockWidget* news_dock_;
    QDockWidget* log_dock_;
    QDockWidget* strategy_dock_;
    QDockWidget* risk_dock_;
    QDockWidget* performance_dock_;
    
    // Widget components
    std::unique_ptr<TradingWidget> trading_widget_;
    std::unique_ptr<ChartWidget> chart_widget_;
    std::unique_ptr<PortfolioWidget> portfolio_widget_;
    std::unique_ptr<OrderBookWidget> orderbook_widget_;
    std::unique_ptr<WatchlistWidget> watchlist_widget_;
    std::unique_ptr<NewsWidget> news_widget_;
    std::unique_ptr<LogWidget> log_widget_;
    std::unique_ptr<StrategyWidget> strategy_widget_;
    std::unique_ptr<RiskWidget> risk_widget_;
    std::unique_ptr<PerformanceWidget> performance_widget_;
    
    // System tray
    QSystemTrayIcon* tray_icon_;
    QMenu* tray_menu_;
    
    // Timers
    QTimer* status_update_timer_;
    QTimer* market_data_timer_;
    QTimer* performance_timer_;
    
    // Trading engine reference
    zenflow::TradingEngine* trading_engine_;
    
    // State
    bool is_connected_;
    bool is_market_open_;
    double account_balance_;
    double total_pnl_;
    QString current_symbol_;
    
    // Constants
    static constexpr int STATUS_UPDATE_INTERVAL = 1000; // 1 second
    static constexpr int MARKET_DATA_INTERVAL = 100;    // 100ms
    static constexpr int PERFORMANCE_INTERVAL = 5000;   // 5 seconds
};

// Helper classes for custom widgets
class StatusIndicator : public QLabel {
    Q_OBJECT
    
public:
    enum Status { Disconnected, Connecting, Connected, Error };
    
    explicit StatusIndicator(QWidget* parent = nullptr);
    void SetStatus(Status status);
    Status GetStatus() const { return current_status_; }

private:
    Status current_status_;
    void UpdateAppearance();
};

class QuickTradeWidget : public QWidget {
    Q_OBJECT
    
public:
    explicit QuickTradeWidget(QWidget* parent = nullptr);
    
signals:
    void BuyRequested(const QString& symbol, double quantity, double price);
    void SellRequested(const QString& symbol, double quantity, double price);
    
private slots:
    void OnBuyClicked();
    void OnSellClicked();
    void OnSymbolChanged();
    
private:
    QComboBox* symbol_combo_;
    QDoubleSpinBox* quantity_spin_;
    QDoubleSpinBox* price_spin_;
    QPushButton* buy_button_;
    QPushButton* sell_button_;
    QLabel* last_price_label_;
    QLabel* bid_ask_label_;
};

#endif // MAIN_WINDOW_H
