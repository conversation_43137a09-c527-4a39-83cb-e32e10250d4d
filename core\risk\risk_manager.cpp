#include "risk_manager.h"
#include <algorithm>
#include <cmath>
#include <numeric>

namespace zenflow {

RiskManager::RiskManager() = default;

RiskManager::~RiskManager() {
    if (is_running_.load()) {
        Stop();
    }
}

bool RiskManager::Initialize() {
    if (is_initialized_.load()) {
        return true;
    }

    try {
        // Initialize internal structures
        risk_limits_.clear();
        risk_metrics_.clear();
        market_data_cache_.clear();
        
        // Initialize portfolio risk
        portfolio_risk_ = PortfolioRisk{};
        portfolio_risk_.last_updated = GetCurrentTimestamp();
        
        is_initialized_.store(true);
        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool RiskManager::Start() {
    if (!is_initialized_.load()) {
        return false;
    }

    if (is_running_.load()) {
        return true;
    }

    is_running_.store(true);
    return true;
}

bool RiskManager::Stop() {
    if (!is_running_.load()) {
        return true;
    }

    is_running_.store(false);
    return true;
}

bool RiskManager::SetRiskLimit(const std::string& symbol, const RiskLimit& limit) {
    if (!limit.IsValid()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(limits_mutex_);
    risk_limits_[symbol] = limit;
    return true;
}

RiskLimit RiskManager::GetRiskLimit(const std::string& symbol) const {
    std::lock_guard<std::mutex> lock(limits_mutex_);
    
    auto it = risk_limits_.find(symbol);
    if (it != risk_limits_.end()) {
        return it->second;
    }
    
    // Return default limit if not found
    RiskLimit default_limit;
    default_limit.symbol = symbol;
    default_limit.max_position_size = 10000;
    default_limit.max_order_size = 1000;
    default_limit.max_daily_loss = -1000;
    default_limit.max_daily_volume = 50000;
    return default_limit;
}

RiskValidationResult RiskManager::ValidateOrder(const OrderRequest& request) {
    RiskValidationResult result;
    result.check_type = RiskCheckType::PRE_TRADE;
    
    if (!risk_checks_enabled_.load()) {
        result.approved = true;
        result.reason = "Risk checks disabled";
        return result;
    }

    if (emergency_stop_.load()) {
        result.approved = false;
        result.reason = "Emergency stop active";
        return result;
    }

    // Validate order size
    if (!ValidateOrderSize(request.symbol, request.quantity)) {
        result.approved = false;
        result.reason = "Order size exceeds limit";
        return result;
    }

    // Validate position size after order
    double position_change = (request.side == OrderSide::BUY) ? request.quantity : -request.quantity;
    if (!ValidatePositionSize(request.symbol, position_change)) {
        result.approved = false;
        result.reason = "Position size would exceed limit";
        return result;
    }

    // Validate daily loss
    if (!ValidateDailyLoss(request.symbol)) {
        result.approved = false;
        result.reason = "Daily loss limit exceeded";
        return result;
    }

    // Validate concentration
    if (!ValidateConcentration(request.symbol, position_change)) {
        result.approved = false;
        result.reason = "Concentration limit exceeded";
        return result;
    }

    // Validate portfolio limits
    if (!ValidatePortfolioLimits()) {
        result.approved = false;
        result.reason = "Portfolio limits exceeded";
        return result;
    }

    result.approved = true;
    result.reason = "All risk checks passed";
    return result;
}

void RiskManager::OnMarketData(const MarketData& data) {
    if (!is_running_.load()) {
        return;
    }

    // Update market data cache
    {
        std::lock_guard<std::mutex> lock(market_data_mutex_);
        market_data_cache_[data.symbol] = data;
    }

    // Update risk metrics
    UpdateRiskMetrics(data.symbol);
    
    // Update portfolio risk
    UpdatePortfolioRisk();
}

void RiskManager::OnPositionEvent(const PositionEvent& event) {
    if (!is_running_.load()) {
        return;
    }

    // Update risk metrics for the symbol
    UpdateRiskMetrics(event.symbol);
    
    // Check position limits
    auto limit = GetRiskLimit(event.symbol);
    if (limit.enabled && std::abs(event.new_quantity) > limit.max_position_size) {
        EmitRiskEvent(RiskEventType::POSITION_LIMIT, event.symbol,
                     "Position limit exceeded", std::abs(event.new_quantity), limit.max_position_size);
    }
}

RiskMetrics RiskManager::GetRiskMetrics(const std::string& symbol) const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    auto it = risk_metrics_.find(symbol);
    if (it != risk_metrics_.end()) {
        return it->second;
    }
    
    // Return empty metrics if not found
    RiskMetrics metrics;
    metrics.symbol = symbol;
    metrics.last_updated = GetCurrentTimestamp();
    return metrics;
}

PortfolioRisk RiskManager::GetPortfolioRisk() const {
    std::lock_guard<std::mutex> lock(portfolio_mutex_);
    return portfolio_risk_;
}

bool RiskManager::ValidatePositionSize(const std::string& symbol, double quantity) {
    auto limit = GetRiskLimit(symbol);
    if (!limit.enabled) {
        return true;
    }

    // Get current position (this would come from position manager in real system)
    auto metrics = GetRiskMetrics(symbol);
    double new_position = metrics.current_position + quantity;
    
    return std::abs(new_position) <= limit.max_position_size;
}

bool RiskManager::ValidateOrderSize(const std::string& symbol, double quantity) {
    auto limit = GetRiskLimit(symbol);
    if (!limit.enabled) {
        return true;
    }

    return quantity <= limit.max_order_size;
}

bool RiskManager::ValidateDailyLoss(const std::string& symbol) {
    auto limit = GetRiskLimit(symbol);
    if (!limit.enabled || limit.max_daily_loss == 0) {
        return true;
    }

    auto metrics = GetRiskMetrics(symbol);
    return metrics.daily_pnl >= limit.max_daily_loss;
}

bool RiskManager::ValidateConcentration(const std::string& symbol, double quantity) {
    auto limit = GetRiskLimit(symbol);
    if (!limit.enabled) {
        return true;
    }

    double position_value = GetPositionValue(symbol);
    double portfolio_value = GetPortfolioValue();
    
    if (portfolio_value == 0) {
        return true;
    }

    double concentration = std::abs(position_value) / portfolio_value;
    return concentration <= limit.max_concentration;
}

bool RiskManager::ValidatePortfolioLimits() {
    auto portfolio_risk = GetPortfolioRisk();
    
    // Check maximum leverage
    if (portfolio_risk.leverage > max_leverage_) {
        return false;
    }

    // Check maximum portfolio loss
    if (portfolio_risk.total_exposure < max_portfolio_loss_) {
        return false;
    }

    return true;
}

void RiskManager::UpdateRiskMetrics(const std::string& symbol) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    RiskMetrics& metrics = risk_metrics_[symbol];
    metrics.symbol = symbol;
    metrics.last_updated = GetCurrentTimestamp();
    
    // Update current position (would get from position manager)
    // metrics.current_position = position_manager->GetPosition(symbol).quantity;
    
    // Calculate VaR
    CalculateVaR(symbol);
    
    // Calculate other risk metrics
    auto returns = GetReturns(symbol);
    if (!returns.empty()) {
        metrics.sharpe_ratio = CalculateSharpeRatio(returns);
        // metrics.max_drawdown = CalculateMaxDrawdown(prices);
    }
}

void RiskManager::UpdatePortfolioRisk() {
    std::lock_guard<std::mutex> lock(portfolio_mutex_);
    
    portfolio_risk_.last_updated = GetCurrentTimestamp();
    
    // Calculate portfolio metrics
    double total_long = 0;
    double total_short = 0;
    
    {
        std::lock_guard<std::mutex> metrics_lock(metrics_mutex_);
        for (const auto& pair : risk_metrics_) {
            const auto& metrics = pair.second;
            double position_value = metrics.current_position * GetCurrentPrice(pair.first);
            
            if (position_value > 0) {
                total_long += position_value;
            } else {
                total_short += std::abs(position_value);
            }
        }
    }
    
    portfolio_risk_.gross_exposure = total_long + total_short;
    portfolio_risk_.net_exposure = total_long - total_short;
    portfolio_risk_.total_exposure = portfolio_risk_.net_exposure;
    
    // Calculate leverage (assuming account value)
    double account_value = 100000; // This would come from account manager
    portfolio_risk_.leverage = portfolio_risk_.gross_exposure / account_value;
    
    // Calculate portfolio VaR
    CalculatePortfolioVaR();
}

void RiskManager::CalculateVaR(const std::string& symbol) {
    auto returns = GetReturns(symbol);
    if (returns.size() < 30) {
        return; // Need at least 30 data points
    }
    
    // Sort returns for percentile calculation
    std::sort(returns.begin(), returns.end());
    
    // Calculate VaR at specified confidence level
    size_t var_index = static_cast<size_t>((1.0 - var_confidence_level_) * returns.size());
    
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    auto& metrics = risk_metrics_[symbol];
    metrics.var_95 = returns[var_index] * GetPositionValue(symbol);
}

void RiskManager::CalculatePortfolioVaR() {
    // Simplified portfolio VaR calculation
    // In practice, this would use correlation matrices and Monte Carlo simulation
    
    double portfolio_var = 0;
    {
        std::lock_guard<std::mutex> lock(metrics_mutex_);
        for (const auto& pair : risk_metrics_) {
            portfolio_var += std::pow(pair.second.var_95, 2);
        }
    }
    
    std::lock_guard<std::mutex> lock(portfolio_mutex_);
    portfolio_risk_.portfolio_var = std::sqrt(portfolio_var);
}

void RiskManager::EmitRiskEvent(RiskEventType type, const std::string& symbol,
                               const std::string& message, double current_value, double limit_value) {
    if (risk_event_callback_) {
        RiskEvent event;
        event.type = type;
        event.symbol = symbol;
        event.message = message;
        event.current_value = current_value;
        event.limit_value = limit_value;
        event.timestamp = GetCurrentTimestamp();
        
        risk_event_callback_(event);
    }
}

double RiskManager::GetCurrentPrice(const std::string& symbol) const {
    std::lock_guard<std::mutex> lock(market_data_mutex_);
    
    auto it = market_data_cache_.find(symbol);
    if (it != market_data_cache_.end()) {
        return it->second.price;
    }
    
    return 0.0;
}

double RiskManager::GetPositionValue(const std::string& symbol) const {
    auto metrics = GetRiskMetrics(symbol);
    double price = GetCurrentPrice(symbol);
    return metrics.current_position * price;
}

double RiskManager::GetPortfolioValue() const {
    double total_value = 0;
    
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    for (const auto& pair : risk_metrics_) {
        total_value += GetPositionValue(pair.first);
    }
    
    return total_value;
}

double RiskManager::CalculateSharpeRatio(const std::vector<double>& returns) const {
    if (returns.empty()) {
        return 0.0;
    }
    
    double mean_return = std::accumulate(returns.begin(), returns.end(), 0.0) / returns.size();
    
    double variance = 0.0;
    for (double ret : returns) {
        variance += std::pow(ret - mean_return, 2);
    }
    variance /= returns.size();
    
    double std_dev = std::sqrt(variance);
    return std_dev > 0 ? (mean_return / std_dev) * std::sqrt(252) : 0.0; // Annualized
}

std::vector<double> RiskManager::GetReturns(const std::string& symbol, int periods) const {
    // This would fetch historical price data and calculate returns
    // For now, return empty vector
    return std::vector<double>();
}

int64_t RiskManager::GetCurrentTimestamp() const {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

} // namespace zenflow
