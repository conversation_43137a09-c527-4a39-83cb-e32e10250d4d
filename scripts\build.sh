#!/bin/bash

# ZenFlow Trading Platform Build Script
# This script builds the entire ZenFlow trading platform including:
# - Core C++ trading engine
# - Qt desktop application
# - C++ backend server
# - React frontend
# - Docker containers

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUILD_TYPE="Release"
BUILD_CORE=true
BUILD_DESKTOP=true
BUILD_BACKEND=true
BUILD_FRONTEND=true
BUILD_DOCKER=false
BUILD_TESTS=false
CLEAN_BUILD=false
INSTALL=false
PACKAGE=false
JOBS=$(nproc)

# Directories
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
INSTALL_DIR="$PROJECT_ROOT/install"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    cat << EOF
ZenFlow Trading Platform Build Script

Usage: $0 [OPTIONS]

Options:
    -h, --help              Show this help message
    -t, --type TYPE         Build type (Debug|Release) [default: Release]
    -j, --jobs N            Number of parallel jobs [default: $(nproc)]
    -c, --clean             Clean build directory before building
    -i, --install           Install after building
    -p, --package           Create packages after building
    --tests                 Build and run tests
    --no-core              Skip core engine build
    --no-desktop           Skip desktop application build
    --no-backend           Skip backend server build
    --no-frontend          Skip frontend build
    --docker               Build Docker containers
    --install-dir DIR      Installation directory [default: $INSTALL_DIR]

Examples:
    $0                      # Build everything in Release mode
    $0 -t Debug --tests     # Build in Debug mode with tests
    $0 --clean --install    # Clean build and install
    $0 --docker             # Build Docker containers
    $0 --no-desktop         # Build without desktop application

EOF
}

check_dependencies() {
    print_header "Checking Dependencies"
    
    # Check CMake
    if ! command -v cmake &> /dev/null; then
        print_error "CMake is required but not installed"
        exit 1
    fi
    print_info "CMake: $(cmake --version | head -n1)"
    
    # Check C++ compiler
    if ! command -v g++ &> /dev/null && ! command -v clang++ &> /dev/null; then
        print_error "C++ compiler (g++ or clang++) is required"
        exit 1
    fi
    
    if command -v g++ &> /dev/null; then
        print_info "GCC: $(g++ --version | head -n1)"
    elif command -v clang++ &> /dev/null; then
        print_info "Clang: $(clang++ --version | head -n1)"
    fi
    
    # Check Qt6 if building desktop
    if [ "$BUILD_DESKTOP" = true ]; then
        if ! command -v qmake6 &> /dev/null && ! command -v qmake &> /dev/null; then
            print_warning "Qt6 not found, desktop application will be skipped"
            BUILD_DESKTOP=false
        else
            print_info "Qt6: Found"
        fi
    fi
    
    # Check Node.js if building frontend
    if [ "$BUILD_FRONTEND" = true ]; then
        if ! command -v node &> /dev/null; then
            print_warning "Node.js not found, frontend will be skipped"
            BUILD_FRONTEND=false
        else
            print_info "Node.js: $(node --version)"
        fi
        
        if ! command -v npm &> /dev/null; then
            print_warning "npm not found, frontend will be skipped"
            BUILD_FRONTEND=false
        else
            print_info "npm: $(npm --version)"
        fi
    fi
    
    # Check Docker if building containers
    if [ "$BUILD_DOCKER" = true ]; then
        if ! command -v docker &> /dev/null; then
            print_error "Docker is required for container builds"
            exit 1
        fi
        print_info "Docker: $(docker --version)"
        
        if ! command -v docker-compose &> /dev/null; then
            print_error "Docker Compose is required for container builds"
            exit 1
        fi
        print_info "Docker Compose: $(docker-compose --version)"
    fi
}

build_core() {
    if [ "$BUILD_CORE" = false ]; then
        return
    fi
    
    print_header "Building Core Trading Engine"
    
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    CMAKE_ARGS=(
        -DCMAKE_BUILD_TYPE="$BUILD_TYPE"
        -DBUILD_CORE=ON
        -DBUILD_DESKTOP="$BUILD_DESKTOP"
        -DBUILD_BACKEND="$BUILD_BACKEND"
        -DBUILD_TESTS="$BUILD_TESTS"
    )
    
    if [ "$INSTALL" = true ]; then
        CMAKE_ARGS+=(-DCMAKE_INSTALL_PREFIX="$INSTALL_DIR")
    fi
    
    print_info "Configuring with CMake..."
    cmake "${CMAKE_ARGS[@]}" "$PROJECT_ROOT"
    
    print_info "Building with $JOBS parallel jobs..."
    cmake --build . --config "$BUILD_TYPE" -j "$JOBS"
    
    if [ "$BUILD_TESTS" = true ]; then
        print_info "Running tests..."
        ctest --output-on-failure
    fi
    
    if [ "$INSTALL" = true ]; then
        print_info "Installing..."
        cmake --install . --config "$BUILD_TYPE"
    fi
    
    if [ "$PACKAGE" = true ]; then
        print_info "Creating packages..."
        cpack
    fi
}

build_frontend() {
    if [ "$BUILD_FRONTEND" = false ]; then
        return
    fi
    
    print_header "Building React Frontend"
    
    cd "$PROJECT_ROOT/frontend"
    
    print_info "Installing dependencies..."
    npm ci
    
    print_info "Building frontend..."
    npm run build
    
    print_info "Frontend build completed"
}

build_docker() {
    if [ "$BUILD_DOCKER" = false ]; then
        return
    fi
    
    print_header "Building Docker Containers"
    
    cd "$PROJECT_ROOT"
    
    print_info "Building Docker images..."
    docker-compose -f docker/docker-compose.yml build
    
    print_info "Docker containers built successfully"
}

cleanup() {
    if [ "$CLEAN_BUILD" = true ]; then
        print_header "Cleaning Build Directory"
        rm -rf "$BUILD_DIR"
        print_info "Build directory cleaned"
    fi
}

main() {
    print_header "ZenFlow Trading Platform Build"
    print_info "Build type: $BUILD_TYPE"
    print_info "Parallel jobs: $JOBS"
    print_info "Components: Core=$BUILD_CORE Desktop=$BUILD_DESKTOP Backend=$BUILD_BACKEND Frontend=$BUILD_FRONTEND"
    
    cleanup
    check_dependencies
    build_core
    build_frontend
    build_docker
    
    print_header "Build Complete"
    print_info "All components built successfully!"
    
    if [ "$INSTALL" = true ]; then
        print_info "Installation directory: $INSTALL_DIR"
    fi
    
    if [ "$BUILD_DOCKER" = true ]; then
        print_info "To start the platform: docker-compose -f docker/docker-compose.yml up -d"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -i|--install)
            INSTALL=true
            shift
            ;;
        -p|--package)
            PACKAGE=true
            shift
            ;;
        --tests)
            BUILD_TESTS=true
            shift
            ;;
        --no-core)
            BUILD_CORE=false
            shift
            ;;
        --no-desktop)
            BUILD_DESKTOP=false
            shift
            ;;
        --no-backend)
            BUILD_BACKEND=false
            shift
            ;;
        --no-frontend)
            BUILD_FRONTEND=false
            shift
            ;;
        --docker)
            BUILD_DOCKER=true
            shift
            ;;
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate build type
if [[ "$BUILD_TYPE" != "Debug" && "$BUILD_TYPE" != "Release" ]]; then
    print_error "Invalid build type: $BUILD_TYPE (must be Debug or Release)"
    exit 1
fi

# Run main function
main
