#include "order_manager.h"
#include <algorithm>
#include <stdexcept>
#include <cmath>

namespace zenflow {

OrderManager::OrderManager() = default;

OrderManager::~OrderManager() {
    if (is_running_.load()) {
        Stop();
    }
}

bool OrderManager::Initialize() {
    if (is_initialized_.load()) {
        return true;
    }

    try {
        // Initialize internal structures
        orders_.clear();
        symbol_orders_.clear();
        strategy_orders_.clear();
        fills_.clear();
        order_fills_.clear();
        positions_.clear();
        
        next_order_id_.store(1);
        is_initialized_.store(true);
        return true;

    } catch (const std::exception& e) {
        return false;
    }
}

bool OrderManager::Start() {
    if (!is_initialized_.load()) {
        return false;
    }

    if (is_running_.load()) {
        return true;
    }

    is_running_.store(true);
    return true;
}

bool OrderManager::Stop() {
    if (!is_running_.load()) {
        return true;
    }

    is_running_.store(false);
    return true;
}

OrderId OrderManager::PlaceOrder(const OrderRequest& request) {
    if (!is_running_.load()) {
        throw std::runtime_error("Order manager not running");
    }

    if (!ValidateOrderRequest(request)) {
        return INVALID_ORDER_ID;
    }

    OrderId order_id = GenerateOrderId();
    int64_t current_time = GetCurrentTimestamp();

    Order order;
    order.id = order_id;
    order.symbol = request.symbol;
    order.type = request.type;
    order.side = request.side;
    order.quantity = request.quantity;
    order.remaining_quantity = request.quantity;
    order.price = request.price;
    order.stop_price = request.stop_price;
    order.status = OrderStatus::PENDING;
    order.time_in_force = request.time_in_force;
    order.strategy_id = request.strategy_id;
    order.created_time = current_time;
    order.updated_time = current_time;
    order.metadata = request.metadata;

    {
        std::lock_guard<std::mutex> lock(orders_mutex_);
        orders_[order_id] = order;
        symbol_orders_[request.symbol].push_back(order_id);
        
        if (!request.strategy_id.empty()) {
            strategy_orders_[request.strategy_id].push_back(order_id);
        }
    }

    // Simulate order submission (in real system, this would go to exchange)
    UpdateOrderStatus(order_id, OrderStatus::SUBMITTED);
    EmitOrderEvent(OrderEventType::ORDER_SUBMITTED, order_id, "Order submitted successfully");

    return order_id;
}

bool OrderManager::CancelOrder(OrderId order_id) {
    if (!is_running_.load()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(orders_mutex_);
    
    auto it = orders_.find(order_id);
    if (it == orders_.end()) {
        return false;
    }

    Order& order = it->second;
    if (!order.IsActive()) {
        return false;
    }

    order.status = OrderStatus::CANCELLED;
    order.updated_time = GetCurrentTimestamp();

    EmitOrderEvent(OrderEventType::ORDER_CANCELLED, order_id, "Order cancelled");
    return true;
}

bool OrderManager::ModifyOrder(OrderId order_id, const OrderModification& modification) {
    if (!is_running_.load()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(orders_mutex_);
    
    auto it = orders_.find(order_id);
    if (it == orders_.end()) {
        return false;
    }

    Order& order = it->second;
    if (!order.IsActive()) {
        return false;
    }

    bool modified = false;
    
    if (modification.modify_quantity && modification.new_quantity > 0) {
        order.quantity = modification.new_quantity;
        order.remaining_quantity = order.quantity - order.filled_quantity;
        modified = true;
    }
    
    if (modification.modify_price && modification.new_price > 0) {
        order.price = modification.new_price;
        modified = true;
    }
    
    if (modification.modify_stop_price) {
        order.stop_price = modification.new_stop_price;
        modified = true;
    }

    if (modified) {
        order.updated_time = GetCurrentTimestamp();
        EmitOrderEvent(OrderEventType::ORDER_MODIFIED, order_id, "Order modified");
    }

    return modified;
}

Order OrderManager::GetOrder(OrderId order_id) const {
    std::lock_guard<std::mutex> lock(orders_mutex_);
    
    auto it = orders_.find(order_id);
    if (it != orders_.end()) {
        return it->second;
    }
    
    return Order{}; // Return empty order if not found
}

std::vector<Order> OrderManager::GetActiveOrders() const {
    std::lock_guard<std::mutex> lock(orders_mutex_);
    
    std::vector<Order> active_orders;
    for (const auto& pair : orders_) {
        if (pair.second.IsActive()) {
            active_orders.push_back(pair.second);
        }
    }
    
    return active_orders;
}

std::vector<Order> OrderManager::GetOrderHistory(const std::string& symbol) const {
    std::lock_guard<std::mutex> lock(orders_mutex_);
    
    std::vector<Order> history;
    
    if (symbol.empty()) {
        // Return all orders
        for (const auto& pair : orders_) {
            history.push_back(pair.second);
        }
    } else {
        // Return orders for specific symbol
        auto it = symbol_orders_.find(symbol);
        if (it != symbol_orders_.end()) {
            for (OrderId order_id : it->second) {
                auto order_it = orders_.find(order_id);
                if (order_it != orders_.end()) {
                    history.push_back(order_it->second);
                }
            }
        }
    }
    
    // Sort by creation time (newest first)
    std::sort(history.begin(), history.end(), 
              [](const Order& a, const Order& b) {
                  return a.created_time > b.created_time;
              });
    
    return history;
}

bool OrderManager::ProcessFill(const Fill& fill) {
    if (!is_running_.load()) {
        return false;
    }

    std::lock_guard<std::mutex> orders_lock(orders_mutex_);
    
    auto it = orders_.find(fill.order_id);
    if (it == orders_.end()) {
        return false;
    }

    Order& order = it->second;
    if (!order.IsActive()) {
        return false;
    }

    // Update order with fill information
    double old_filled = order.filled_quantity;
    order.filled_quantity += fill.quantity;
    order.remaining_quantity = order.quantity - order.filled_quantity;
    
    // Calculate average fill price
    if (order.filled_quantity > 0) {
        order.average_fill_price = CalculateAveragePrice(
            order.average_fill_price, old_filled, fill.price, fill.quantity);
    }

    // Update order status
    if (order.remaining_quantity <= 0.0001) { // Account for floating point precision
        order.status = OrderStatus::FILLED;
        order.remaining_quantity = 0;
        EmitOrderEvent(OrderEventType::ORDER_FILLED, fill.order_id, "Order fully filled");
    } else {
        order.status = OrderStatus::PARTIALLY_FILLED;
        EmitOrderEvent(OrderEventType::ORDER_PARTIALLY_FILLED, fill.order_id, "Order partially filled");
    }

    order.updated_time = GetCurrentTimestamp();

    // Store fill
    {
        std::lock_guard<std::mutex> fills_lock(fills_mutex_);
        fills_.push_back(fill);
        order_fills_[fill.order_id].push_back(fill);
    }

    // Update position
    UpdatePosition(fill);

    // Call fill callback
    if (fill_callback_) {
        fill_callback_(fill);
    }

    return true;
}

Position OrderManager::GetPosition(const std::string& symbol) const {
    std::lock_guard<std::mutex> lock(positions_mutex_);
    
    auto it = positions_.find(symbol);
    if (it != positions_.end()) {
        return it->second;
    }
    
    return Position{symbol, 0, 0, 0, 0, GetCurrentTimestamp()};
}

std::vector<Position> OrderManager::GetAllPositions() const {
    std::lock_guard<std::mutex> lock(positions_mutex_);
    
    std::vector<Position> positions;
    for (const auto& pair : positions_) {
        if (!pair.second.IsFlat()) {
            positions.push_back(pair.second);
        }
    }
    
    return positions;
}

double OrderManager::GetTotalPnL() const {
    std::lock_guard<std::mutex> lock(positions_mutex_);
    
    double total_pnl = 0;
    for (const auto& pair : positions_) {
        total_pnl += pair.second.realized_pnl + pair.second.unrealized_pnl;
    }
    
    return total_pnl;
}

void OrderManager::UpdatePosition(const Fill& fill) {
    std::lock_guard<std::mutex> lock(positions_mutex_);
    
    Position& position = positions_[fill.symbol];
    position.symbol = fill.symbol;
    
    double old_quantity = position.quantity;
    double fill_quantity = (fill.side == OrderSide::BUY) ? fill.quantity : -fill.quantity;
    
    if (position.IsFlat()) {
        // Opening new position
        position.quantity = fill_quantity;
        position.average_price = fill.price;
    } else if ((position.IsLong() && fill.side == OrderSide::BUY) ||
               (position.IsShort() && fill.side == OrderSide::SELL)) {
        // Adding to existing position
        position.average_price = CalculateAveragePrice(
            position.average_price, std::abs(position.quantity), 
            fill.price, fill.quantity);
        position.quantity += fill_quantity;
    } else {
        // Reducing or closing position
        double abs_old_qty = std::abs(position.quantity);
        if (fill.quantity >= abs_old_qty) {
            // Closing and potentially reversing position
            double realized_pnl = abs_old_qty * (fill.price - position.average_price) * 
                                 (position.IsLong() ? 1 : -1);
            position.realized_pnl += realized_pnl;
            
            double remaining_qty = fill.quantity - abs_old_qty;
            if (remaining_qty > 0) {
                position.quantity = (fill.side == OrderSide::BUY) ? remaining_qty : -remaining_qty;
                position.average_price = fill.price;
            } else {
                position.quantity = 0;
                position.average_price = 0;
            }
        } else {
            // Partial close
            double close_qty = fill.quantity;
            double realized_pnl = close_qty * (fill.price - position.average_price) * 
                                 (position.IsLong() ? 1 : -1);
            position.realized_pnl += realized_pnl;
            position.quantity += fill_quantity;
        }
    }
    
    position.updated_time = GetCurrentTimestamp();
    
    // Emit position event
    EmitPositionEvent(fill.symbol, old_quantity, position.quantity, fill.price);
}

OrderId OrderManager::GenerateOrderId() {
    return next_order_id_.fetch_add(1);
}

bool OrderManager::ValidateOrderRequest(const OrderRequest& request) const {
    return request.IsValid();
}

void OrderManager::UpdateOrderStatus(OrderId order_id, OrderStatus new_status) {
    std::lock_guard<std::mutex> lock(orders_mutex_);
    
    auto it = orders_.find(order_id);
    if (it != orders_.end()) {
        it->second.status = new_status;
        it->second.updated_time = GetCurrentTimestamp();
    }
}

void OrderManager::EmitOrderEvent(OrderEventType type, OrderId order_id, const std::string& message) {
    if (event_callback_) {
        OrderEvent event;
        event.type = type;
        event.order_id = order_id;
        event.message = message;
        event.timestamp = GetCurrentTimestamp();
        
        auto order = GetOrder(order_id);
        event.symbol = order.symbol;
        
        event_callback_(event);
    }
}

void OrderManager::EmitPositionEvent(const std::string& symbol, double old_qty, double new_qty, double price) {
    if (position_callback_) {
        PositionEvent event;
        event.symbol = symbol;
        event.old_quantity = old_qty;
        event.new_quantity = new_qty;
        event.price = price;
        event.timestamp = GetCurrentTimestamp();
        
        position_callback_(event);
    }
}

int64_t OrderManager::GetCurrentTimestamp() const {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

double OrderManager::CalculateAveragePrice(double current_avg, double current_qty, 
                                          double fill_price, double fill_qty) const {
    if (current_qty + fill_qty == 0) {
        return 0;
    }
    return (current_avg * current_qty + fill_price * fill_qty) / (current_qty + fill_qty);
}

} // namespace zenflow
