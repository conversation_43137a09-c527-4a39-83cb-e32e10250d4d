#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <functional>
#include <chrono>
#include <queue>

namespace zenflow {

enum class MarketDataType {
    TICK,
    QUOTE,
    TRADE,
    ORDER_BOOK,
    CANDLE
};

struct MarketData {
    std::string symbol;
    MarketDataType type;
    double price;
    double volume;
    double bid;
    double ask;
    int64_t timestamp;
    std::string exchange;
    
    // Additional fields for different data types
    std::unordered_map<std::string, double> extended_data;
    
    std::string ToString() const {
        return symbol + " " + std::to_string(price) + " @ " + std::to_string(timestamp);
    }
};

struct Candle {
    std::string symbol;
    int64_t timestamp;
    double open;
    double high;
    double low;
    double close;
    double volume;
    std::string timeframe; // "1m", "5m", "1h", "1d", etc.
};

struct OrderBookLevel {
    double price;
    double quantity;
    int order_count;
};

struct OrderBook {
    std::string symbol;
    int64_t timestamp;
    std::vector<OrderBookLevel> bids;
    std::vector<OrderBookLevel> asks;
};

class MarketDataProvider {
public:
    virtual ~MarketDataProvider() = default;
    virtual bool Initialize() = 0;
    virtual bool Start() = 0;
    virtual bool Stop() = 0;
    virtual bool Subscribe(const std::string& symbol) = 0;
    virtual bool Unsubscribe(const std::string& symbol) = 0;
    virtual void SetDataCallback(std::function<void(const MarketData&)> callback) = 0;
    virtual std::string GetProviderName() const = 0;
};

// Alpha Vantage API Provider
class AlphaVantageProvider : public MarketDataProvider {
public:
    explicit AlphaVantageProvider(const std::string& api_key);
    bool Initialize() override;
    bool Start() override;
    bool Stop() override;
    bool Subscribe(const std::string& symbol) override;
    bool Unsubscribe(const std::string& symbol) override;
    void SetDataCallback(std::function<void(const MarketData&)> callback) override;
    std::string GetProviderName() const override { return "AlphaVantage"; }

private:
    std::string api_key_;
    std::function<void(const MarketData&)> data_callback_;
    std::atomic<bool> is_running_{false};
    std::thread polling_thread_;
    std::vector<std::string> subscribed_symbols_;
    std::mutex symbols_mutex_;
    
    void PollData();
    MarketData ParseQuoteData(const std::string& json_response, const std::string& symbol);
};

// IEX Cloud Provider
class IEXCloudProvider : public MarketDataProvider {
public:
    explicit IEXCloudProvider(const std::string& api_key);
    bool Initialize() override;
    bool Start() override;
    bool Stop() override;
    bool Subscribe(const std::string& symbol) override;
    bool Unsubscribe(const std::string& symbol) override;
    void SetDataCallback(std::function<void(const MarketData&)> callback) override;
    std::string GetProviderName() const override { return "IEXCloud"; }

private:
    std::string api_key_;
    std::function<void(const MarketData&)> data_callback_;
    std::atomic<bool> is_running_{false};
    std::thread polling_thread_;
    std::vector<std::string> subscribed_symbols_;
    std::mutex symbols_mutex_;
    
    void PollData();
    MarketData ParseQuoteData(const std::string& json_response, const std::string& symbol);
};

class MarketDataHandler {
public:
    MarketDataHandler();
    ~MarketDataHandler();

    // Lifecycle management
    bool Initialize();
    bool Start();
    bool Stop();
    bool IsRunning() const { return is_running_.load(); }

    // Provider management
    bool AddProvider(std::unique_ptr<MarketDataProvider> provider);
    bool RemoveProvider(const std::string& provider_name);
    std::vector<std::string> GetProviderNames() const;

    // Subscription management
    bool Subscribe(const std::string& symbol, const std::string& provider_name = "");
    bool Unsubscribe(const std::string& symbol, const std::string& provider_name = "");
    std::vector<std::string> GetSubscribedSymbols() const;

    // Data access
    MarketData GetLatestData(const std::string& symbol) const;
    std::vector<MarketData> GetHistoricalData(const std::string& symbol, 
                                              int64_t start_time, 
                                              int64_t end_time) const;
    std::vector<Candle> GetCandles(const std::string& symbol, 
                                   const std::string& timeframe,
                                   int count = 100) const;

    // Callbacks
    void SetDataCallback(std::function<void(const MarketData&)> callback);
    void SetCandleCallback(std::function<void(const Candle&)> callback);

    // Statistics
    size_t GetTotalTicksReceived() const { return total_ticks_received_.load(); }
    double GetAverageLatency() const;
    std::unordered_map<std::string, size_t> GetSymbolTickCounts() const;

private:
    // Providers
    std::vector<std::unique_ptr<MarketDataProvider>> providers_;
    mutable std::mutex providers_mutex_;

    // Data storage
    std::unordered_map<std::string, MarketData> latest_data_;
    std::unordered_map<std::string, std::queue<MarketData>> historical_data_;
    mutable std::mutex data_mutex_;

    // Candle generation
    std::unordered_map<std::string, std::unordered_map<std::string, Candle>> current_candles_;
    std::mutex candles_mutex_;

    // State
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_initialized_{false};

    // Statistics
    std::atomic<size_t> total_ticks_received_{0};
    std::unordered_map<std::string, size_t> symbol_tick_counts_;
    mutable std::mutex stats_mutex_;

    // Callbacks
    std::function<void(const MarketData&)> data_callback_;
    std::function<void(const Candle&)> candle_callback_;

    // Internal methods
    void OnDataReceived(const MarketData& data);
    void UpdateCandles(const MarketData& data);
    void UpdateStatistics(const MarketData& data);
    Candle CreateCandleFromTicks(const std::string& symbol, 
                                const std::string& timeframe,
                                int64_t start_time);
    
    // Helper methods
    int64_t GetCurrentTimestamp() const;
    int64_t AlignTimestamp(int64_t timestamp, const std::string& timeframe) const;
    std::string HttpGet(const std::string& url) const;
};

} // namespace zenflow
