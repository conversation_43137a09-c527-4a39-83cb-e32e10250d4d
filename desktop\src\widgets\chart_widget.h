#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QTimer>
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <vector>
#include <memory>

struct CandleData {
    double open;
    double high;
    double low;
    double close;
    double volume;
    int64_t timestamp;
};

class ChartWidget : public QWidget {
    Q_OBJECT

public:
    explicit ChartWidget(QWidget* parent = nullptr);
    ~ChartWidget() = default;

    void SetSymbol(const QString& symbol);
    void UpdateData();
    void AddCandleData(const CandleData& candle);

protected:
    void paintEvent(QPaintEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    void resizeEvent(QResizeEvent* event) override;

private slots:
    void OnTimeframeChanged();
    void OnIndicatorToggled();
    void OnZoomIn();
    void OnZoomOut();
    void OnAutoScale();

private:
    void SetupUI();
    void SetupToolbar();
    void DrawChart(QPainter& painter);
    void DrawCandles(QPainter& painter);
    void DrawVolume(QPainter& painter);
    void DrawGrid(QPainter& painter);
    void DrawCrosshair(QPainter& painter);
    void DrawIndicators(QPainter& painter);
    void UpdateScale();
    void GenerateTestData();
    
    double PriceToY(double price) const;
    double YToPrice(double y) const;
    double VolumeToY(double volume) const;
    int TimeToX(int64_t timestamp) const;
    int64_t XToTime(int x) const;

    // UI Components
    QVBoxLayout* main_layout_;
    QHBoxLayout* toolbar_layout_;
    QLabel* symbol_label_;
    QComboBox* timeframe_combo_;
    QPushButton* zoom_in_button_;
    QPushButton* zoom_out_button_;
    QPushButton* auto_scale_button_;
    QPushButton* indicators_button_;
    
    // Chart data
    QString current_symbol_;
    std::vector<CandleData> candle_data_;
    
    // Chart settings
    double price_min_;
    double price_max_;
    double volume_max_;
    int visible_candles_;
    int start_index_;
    double zoom_factor_;
    
    // Mouse interaction
    QPoint last_mouse_pos_;
    bool is_dragging_;
    bool show_crosshair_;
    QPoint crosshair_pos_;
    
    // Chart dimensions
    QRect chart_rect_;
    QRect volume_rect_;
    int margin_left_;
    int margin_right_;
    int margin_top_;
    int margin_bottom_;
    int volume_height_;
    
    // Colors
    QColor background_color_;
    QColor grid_color_;
    QColor text_color_;
    QColor bull_candle_color_;
    QColor bear_candle_color_;
    QColor volume_color_;
    QColor crosshair_color_;
    
    // Timer for data updates
    QTimer* update_timer_;
    
    // Constants
    static constexpr int DEFAULT_VISIBLE_CANDLES = 100;
    static constexpr double DEFAULT_ZOOM_FACTOR = 1.0;
    static constexpr int VOLUME_HEIGHT_RATIO = 4; // 1/4 of chart height
};

// Simple moving average indicator
class MovingAverageIndicator {
public:
    explicit MovingAverageIndicator(int period = 20);
    
    void AddPrice(double price);
    double GetValue() const;
    bool IsReady() const;
    void Reset();
    
private:
    int period_;
    std::vector<double> prices_;
    double sum_;
    int current_index_;
    bool is_ready_;
};

// Technical analysis helper
class TechnicalAnalysis {
public:
    static std::vector<double> CalculateSMA(const std::vector<CandleData>& data, int period);
    static std::vector<double> CalculateEMA(const std::vector<CandleData>& data, int period);
    static std::vector<double> CalculateRSI(const std::vector<CandleData>& data, int period = 14);
    static std::pair<std::vector<double>, std::vector<double>> CalculateBollingerBands(
        const std::vector<CandleData>& data, int period = 20, double std_dev = 2.0);
    
private:
    static double CalculateStandardDeviation(const std::vector<double>& values, double mean);
};

#endif // CHART_WIDGET_H
