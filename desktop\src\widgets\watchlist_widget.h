#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTableWidget>
#include <QLineEdit>
#include <QPushButton>
#include <QTimer>
#include <QMenu>
#include <QContextMenuEvent>

class WatchlistWidget : public QWidget {
    Q_OBJECT

public:
    explicit WatchlistWidget(QWidget* parent = nullptr);
    ~WatchlistWidget() = default;

    void AddSymbol(const QString& symbol);
    void RemoveSymbol(const QString& symbol);
    void UpdatePrices();

signals:
    void SymbolSelected(const QString& symbol);
    void SymbolDoubleClicked(const QString& symbol);

private slots:
    void OnAddSymbol();
    void OnRemoveSymbol();
    void OnSymbolClicked(int row, int column);
    void OnSymbolDoubleClicked(int row, int column);
    void OnContextMenu(const QPoint& pos);
    void OnRefreshPrices();

private:
    void SetupUI();
    void PopulateTable();
    void UpdateSymbolData(int row, const QString& symbol);
    
    QVBoxLayout* main_layout_;
    QHBoxLayout* controls_layout_;
    QLineEdit* symbol_input_;
    QPushButton* add_button_;
    QPushButton* remove_button_;
    QTableWidget* watchlist_table_;
    QTimer* refresh_timer_;
    QMenu* context_menu_;
    
    QStringList symbols_;
    
    static constexpr int REFRESH_INTERVAL = 1000; // 1 second
};

#endif // WATCHLIST_WIDGET_H
