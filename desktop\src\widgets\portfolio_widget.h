#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTableWidget>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QGroupBox>
#include "../../../core/engine/trading_engine.h"

class PortfolioWidget : public QWidget {
    Q_OBJECT

public:
    explicit PortfolioWidget(QWidget* parent = nullptr);
    ~PortfolioWidget() = default;

    void SetTradingEngine(zenflow::TradingEngine* engine);
    void UpdatePortfolio();

private slots:
    void OnRefreshClicked();
    void OnClosePosition();
    void OnPositionDoubleClicked(int row, int column);

private:
    void SetupUI();
    void SetupSummary();
    void SetupPositionsTable();
    void UpdateSummaryLabels();
    void PopulatePositionsTable();
    
    QVBoxLayout* main_layout_;
    
    // Summary section
    QGroupBox* summary_group_;
    QGridLayout* summary_layout_;
    QLabel* total_value_label_;
    QLabel* total_pnl_label_;
    QLabel* day_pnl_label_;
    QLabel* buying_power_label_;
    
    // Positions table
    QTableWidget* positions_table_;
    QPushButton* refresh_button_;
    
    zenflow::TradingEngine* trading_engine_;
    QTimer* update_timer_;
    
    static constexpr int UPDATE_INTERVAL = 2000; // 2 seconds
};

#endif // PORTFOLIO_WIDGET_H
