#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QSplashScreen>
#include <QPixmap>
#include <QTimer>
#include <QFontDatabase>

#include "main_window.h"
#include "application_settings.h"
#include "theme_manager.h"
#include "../../core/engine/trading_engine.h"
#include "../../core/utils/logger.h"

class ZenFlowApplication : public QApplication {
    Q_OBJECT

public:
    ZenFlowApplication(int &argc, char **argv) : QApplication(argc, argv) {
        setApplicationName("ZenFlow Trading");
        setApplicationVersion("1.0.0");
        setOrganizationName("ZenFlow");
        setOrganizationDomain("zenflow.trade");
        
        // Set application properties
        setApplicationDisplayName("ZenFlow Trading Platform");
        setDesktopFileName("zenflow-trading");
        
        // Initialize logging
        InitializeLogging();
        
        // Initialize settings
        settings_ = std::make_unique<ApplicationSettings>();
        
        // Initialize theme manager
        theme_manager_ = std::make_unique<ThemeManager>();
        
        // Initialize trading engine
        InitializeTradingEngine();
        
        LOG_INFO("ZenFlow Trading application initialized");
    }
    
    ~ZenFlowApplication() {
        if (trading_engine_) {
            trading_engine_->Stop();
        }
        LOG_INFO("ZenFlow Trading application shutting down");
    }
    
    ApplicationSettings* GetSettings() const { return settings_.get(); }
    ThemeManager* GetThemeManager() const { return theme_manager_.get(); }
    zenflow::TradingEngine* GetTradingEngine() const { return trading_engine_.get(); }

private slots:
    void OnAboutToQuit() {
        LOG_INFO("Application about to quit");
        if (trading_engine_) {
            trading_engine_->Stop();
        }
    }

private:
    std::unique_ptr<ApplicationSettings> settings_;
    std::unique_ptr<ThemeManager> theme_manager_;
    std::unique_ptr<zenflow::TradingEngine> trading_engine_;
    
    void InitializeLogging() {
        // Create logs directory
        QString log_dir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
        QDir().mkpath(log_dir);
        
        // Initialize global logger
        auto logger = std::make_unique<zenflow::Logger>("ZenFlow", zenflow::LogLevel::INFO);
        
        // Add console sink
        logger->AddSink(std::make_unique<zenflow::ConsoleSink>(true));
        
        // Add file sink
        QString log_file = log_dir + "/zenflow.log";
        logger->AddSink(std::make_unique<zenflow::FileSink>(log_file.toStdString()));
        
        // Set as global logger
        zenflow::Logger::SetGlobalLogger(std::move(logger));
    }
    
    void InitializeTradingEngine() {
        zenflow::EngineConfig config;
        config.enable_logging = true;
        config.enable_risk_management = true;
        config.max_threads = std::thread::hardware_concurrency();
        
        trading_engine_ = zenflow::CreateTradingEngine(config);
        
        if (!trading_engine_->Initialize()) {
            QMessageBox::critical(nullptr, "Error", 
                "Failed to initialize trading engine. Please check the logs.");
            quit();
            return;
        }
        
        if (!trading_engine_->Start()) {
            QMessageBox::critical(nullptr, "Error", 
                "Failed to start trading engine. Please check the logs.");
            quit();
            return;
        }
        
        LOG_INFO("Trading engine initialized and started successfully");
    }
};

void ShowSplashScreen(QApplication& app) {
    QPixmap splash_pixmap(400, 300);
    splash_pixmap.fill(QColor(45, 45, 48)); // Dark background
    
    QSplashScreen splash(splash_pixmap);
    splash.setWindowFlags(Qt::WindowStaysOnTopHint | Qt::SplashScreen);
    
    // Add text to splash screen
    splash.showMessage("Loading ZenFlow Trading Platform...", 
                      Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    splash.show();
    
    app.processEvents();
    
    // Simulate loading time
    QTimer::singleShot(2000, &splash, &QSplashScreen::close);
    
    // Keep splash visible for at least 2 seconds
    QTimer timer;
    timer.setSingleShot(true);
    QEventLoop loop;
    QObject::connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    timer.start(2000);
    loop.exec();
}

int main(int argc, char *argv[]) {
    // Enable high DPI support
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    ZenFlowApplication app(argc, argv);
    
    // Connect quit signal
    QObject::connect(&app, &QApplication::aboutToQuit, &app, &ZenFlowApplication::OnAboutToQuit);
    
    // Show splash screen
    ShowSplashScreen(app);
    
    // Load custom fonts
    QFontDatabase::addApplicationFont(":/fonts/Roboto-Regular.ttf");
    QFontDatabase::addApplicationFont(":/fonts/Roboto-Bold.ttf");
    
    // Apply theme
    app.GetThemeManager()->ApplyTheme(app.GetSettings()->GetTheme());
    
    // Create and show main window
    MainWindow main_window;
    main_window.show();
    
    LOG_INFO("Main window displayed, entering event loop");
    
    return app.exec();
}

#include "main.moc"
