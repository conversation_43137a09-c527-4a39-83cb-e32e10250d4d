#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <pqxx/pqxx>
#include <json/json.h>

struct DatabaseConfig {
    std::string host = "localhost";
    int port = 5432;
    std::string database = "zenflow_trading";
    std::string username = "zenflow";
    std::string password = "zenflow_secure_password";
    int max_connections = 10;
    int connection_timeout = 30;
    bool enable_ssl = false;
    std::string ssl_cert_file;
    std::string ssl_key_file;
};

struct User {
    std::string id;
    std::string username;
    std::string email;
    std::string password_hash;
    bool is_admin = false;
    bool is_active = true;
    int64_t created_at;
    int64_t updated_at;
    Json::Value metadata;
};

struct Account {
    std::string id;
    std::string user_id;
    std::string name;
    std::string account_type;
    double balance = 0.0;
    double buying_power = 0.0;
    bool is_active = true;
    int64_t created_at;
    int64_t updated_at;
};

class DatabaseManager {
public:
    explicit DatabaseManager(const DatabaseConfig& config);
    ~DatabaseManager();

    bool Initialize();
    bool Connect();
    void Disconnect();
    bool IsConnected() const;

    // User management
    bool CreateUser(const User& user);
    bool GetUser(const std::string& user_id, User& user);
    bool GetUserByUsername(const std::string& username, User& user);
    bool UpdateUser(const User& user);
    bool DeleteUser(const std::string& user_id);
    std::vector<User> GetAllUsers();

    // Account management
    bool CreateAccount(const Account& account);
    bool GetAccount(const std::string& account_id, Account& account);
    std::vector<Account> GetUserAccounts(const std::string& user_id);
    bool UpdateAccount(const Account& account);
    bool DeleteAccount(const std::string& account_id);

    // Trading data
    bool SaveOrder(const std::string& order_json);
    bool UpdateOrder(const std::string& order_id, const std::string& order_json);
    bool GetOrder(const std::string& order_id, std::string& order_json);
    std::vector<std::string> GetUserOrders(const std::string& user_id, int limit = 100);

    bool SavePosition(const std::string& position_json);
    bool UpdatePosition(const std::string& account_id, const std::string& symbol, const std::string& position_json);
    bool GetPosition(const std::string& account_id, const std::string& symbol, std::string& position_json);
    std::vector<std::string> GetAccountPositions(const std::string& account_id);

    bool SaveFill(const std::string& fill_json);
    std::vector<std::string> GetOrderFills(const std::string& order_id);

    // Market data
    bool SaveMarketData(const std::string& symbol, const std::string& market_data_json);
    bool GetLatestMarketData(const std::string& symbol, std::string& market_data_json);
    std::vector<std::string> GetHistoricalMarketData(const std::string& symbol, 
                                                    int64_t start_time, int64_t end_time);

    bool SaveCandle(const std::string& candle_json);
    std::vector<std::string> GetCandles(const std::string& symbol, const std::string& timeframe,
                                       int64_t start_time, int64_t end_time);

    // Strategy data
    bool SaveStrategy(const std::string& strategy_json);
    bool UpdateStrategy(const std::string& strategy_id, const std::string& strategy_json);
    bool GetStrategy(const std::string& strategy_id, std::string& strategy_json);
    std::vector<std::string> GetUserStrategies(const std::string& user_id);
    bool DeleteStrategy(const std::string& strategy_id);

    // Risk data
    bool SaveRiskMetrics(const std::string& risk_metrics_json);
    bool GetLatestRiskMetrics(const std::string& account_id, std::string& risk_metrics_json);

    // Utility methods
    bool ExecuteQuery(const std::string& query);
    bool ExecuteQuery(const std::string& query, pqxx::result& result);
    std::string EscapeString(const std::string& input);
    
    // Transaction support
    class Transaction {
    public:
        explicit Transaction(DatabaseManager* db);
        ~Transaction();
        
        bool Commit();
        void Rollback();
        bool ExecuteQuery(const std::string& query);
        bool ExecuteQuery(const std::string& query, pqxx::result& result);
        
    private:
        DatabaseManager* db_;
        std::unique_ptr<pqxx::work> transaction_;
        bool committed_;
    };
    
    std::unique_ptr<Transaction> BeginTransaction();

private:
    DatabaseConfig config_;
    std::unique_ptr<pqxx::connection> connection_;
    bool is_connected_;
    
    // Connection pool (simplified)
    std::vector<std::unique_ptr<pqxx::connection>> connection_pool_;
    std::mutex pool_mutex_;
    
    std::string BuildConnectionString() const;
    bool TestConnection();
    void InitializeTables();
    
    // Helper methods
    User ResultToUser(const pqxx::row& row);
    Account ResultToAccount(const pqxx::row& row);
    std::string GenerateUUID();
    int64_t GetCurrentTimestamp();
};

// Database connection pool
class ConnectionPool {
public:
    explicit ConnectionPool(const DatabaseConfig& config, size_t pool_size = 10);
    ~ConnectionPool();
    
    std::unique_ptr<pqxx::connection> GetConnection();
    void ReturnConnection(std::unique_ptr<pqxx::connection> conn);
    
private:
    DatabaseConfig config_;
    std::queue<std::unique_ptr<pqxx::connection>> available_connections_;
    std::mutex pool_mutex_;
    std::condition_variable pool_condition_;
    size_t pool_size_;
    size_t active_connections_;
    
    std::unique_ptr<pqxx::connection> CreateConnection();
};

// Database migration helper
class MigrationManager {
public:
    explicit MigrationManager(DatabaseManager* db);
    
    bool RunMigrations();
    bool RunMigration(const std::string& migration_file);
    int GetCurrentVersion();
    bool SetVersion(int version);
    
private:
    DatabaseManager* db_;
    std::string migrations_path_;
    
    bool CreateMigrationsTable();
    std::vector<std::string> GetMigrationFiles();
};

#endif // DATABASE_MANAGER_H
