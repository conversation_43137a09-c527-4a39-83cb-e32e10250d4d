@echo off
setlocal enabledelayedexpansion

REM ZenFlow Trading Platform Build Script for Windows
REM This script builds the entire ZenFlow trading platform

set "BUILD_TYPE=Release"
set "BUILD_CORE=true"
set "BUILD_DESKTOP=true"
set "BUILD_BACKEND=true"
set "BUILD_FRONTEND=true"
set "BUILD_DOCKER=false"
set "BUILD_TESTS=false"
set "CLEAN_BUILD=false"
set "INSTALL=false"
set "PACKAGE=false"
set "JOBS=%NUMBER_OF_PROCESSORS%"

REM Get script directory
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "BUILD_DIR=%PROJECT_ROOT%\build"
set "INSTALL_DIR=%PROJECT_ROOT%\install"

echo ================================
echo ZenFlow Trading Platform Build
echo ================================

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :check_deps
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-t" (
    set "BUILD_TYPE=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--type" (
    set "BUILD_TYPE=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="-c" (
    set "CLEAN_BUILD=true"
    shift
    goto :parse_args
)
if "%~1"=="--clean" (
    set "CLEAN_BUILD=true"
    shift
    goto :parse_args
)
if "%~1"=="-i" (
    set "INSTALL=true"
    shift
    goto :parse_args
)
if "%~1"=="--install" (
    set "INSTALL=true"
    shift
    goto :parse_args
)
if "%~1"=="--tests" (
    set "BUILD_TESTS=true"
    shift
    goto :parse_args
)
if "%~1"=="--no-core" (
    set "BUILD_CORE=false"
    shift
    goto :parse_args
)
if "%~1"=="--no-desktop" (
    set "BUILD_DESKTOP=false"
    shift
    goto :parse_args
)
if "%~1"=="--no-backend" (
    set "BUILD_BACKEND=false"
    shift
    goto :parse_args
)
if "%~1"=="--no-frontend" (
    set "BUILD_FRONTEND=false"
    shift
    goto :parse_args
)
if "%~1"=="--docker" (
    set "BUILD_DOCKER=true"
    shift
    goto :parse_args
)
echo Unknown option: %~1
goto :show_help

:show_help
echo ZenFlow Trading Platform Build Script for Windows
echo.
echo Usage: %~nx0 [OPTIONS]
echo.
echo Options:
echo     -h, --help              Show this help message
echo     -t, --type TYPE         Build type (Debug^|Release) [default: Release]
echo     -c, --clean             Clean build directory before building
echo     -i, --install           Install after building
echo     --tests                 Build and run tests
echo     --no-core              Skip core engine build
echo     --no-desktop           Skip desktop application build
echo     --no-backend           Skip backend server build
echo     --no-frontend          Skip frontend build
echo     --docker               Build Docker containers
echo.
echo Examples:
echo     %~nx0                      # Build everything in Release mode
echo     %~nx0 -t Debug --tests     # Build in Debug mode with tests
echo     %~nx0 --clean --install    # Clean build and install
echo     %~nx0 --docker             # Build Docker containers
echo.
goto :eof

:check_deps
echo [INFO] Checking Dependencies...

REM Check CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] CMake is required but not installed
    exit /b 1
)
echo [INFO] CMake: Found

REM Check Visual Studio or MinGW
cl >nul 2>&1
if not errorlevel 1 (
    echo [INFO] MSVC: Found
) else (
    g++ --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] C++ compiler (MSVC or MinGW) is required
        exit /b 1
    )
    echo [INFO] MinGW: Found
)

REM Check Qt6 if building desktop
if "%BUILD_DESKTOP%"=="true" (
    qmake --version >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Qt6 not found, desktop application will be skipped
        set "BUILD_DESKTOP=false"
    ) else (
        echo [INFO] Qt6: Found
    )
)

REM Check Node.js if building frontend
if "%BUILD_FRONTEND%"=="true" (
    node --version >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Node.js not found, frontend will be skipped
        set "BUILD_FRONTEND=false"
    ) else (
        echo [INFO] Node.js: Found
    )
    
    npm --version >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] npm not found, frontend will be skipped
        set "BUILD_FRONTEND=false"
    ) else (
        echo [INFO] npm: Found
    )
)

REM Check Docker if building containers
if "%BUILD_DOCKER%"=="true" (
    docker --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Docker is required for container builds
        exit /b 1
    )
    echo [INFO] Docker: Found
    
    docker-compose --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Docker Compose is required for container builds
        exit /b 1
    )
    echo [INFO] Docker Compose: Found
)

:cleanup
if "%CLEAN_BUILD%"=="true" (
    echo [INFO] Cleaning build directory...
    if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
    echo [INFO] Build directory cleaned
)

:build_core
if "%BUILD_CORE%"=="false" goto :build_frontend

echo ================================
echo Building Core Trading Engine
echo ================================

if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
cd /d "%BUILD_DIR%"

echo [INFO] Configuring with CMake...
set CMAKE_ARGS=-DCMAKE_BUILD_TYPE=%BUILD_TYPE% -DBUILD_CORE=ON -DBUILD_DESKTOP=%BUILD_DESKTOP% -DBUILD_BACKEND=%BUILD_BACKEND% -DBUILD_TESTS=%BUILD_TESTS%

if "%INSTALL%"=="true" (
    set CMAKE_ARGS=%CMAKE_ARGS% -DCMAKE_INSTALL_PREFIX="%INSTALL_DIR%"
)

cmake %CMAKE_ARGS% "%PROJECT_ROOT%"
if errorlevel 1 (
    echo [ERROR] CMake configuration failed
    exit /b 1
)

echo [INFO] Building with %JOBS% parallel jobs...
cmake --build . --config %BUILD_TYPE% --parallel %JOBS%
if errorlevel 1 (
    echo [ERROR] Build failed
    exit /b 1
)

if "%BUILD_TESTS%"=="true" (
    echo [INFO] Running tests...
    ctest --output-on-failure
)

if "%INSTALL%"=="true" (
    echo [INFO] Installing...
    cmake --install . --config %BUILD_TYPE%
)

:build_frontend
if "%BUILD_FRONTEND%"=="false" goto :build_docker

echo ================================
echo Building React Frontend
echo ================================

cd /d "%PROJECT_ROOT%\frontend"

echo [INFO] Installing dependencies...
call npm ci
if errorlevel 1 (
    echo [ERROR] npm install failed
    exit /b 1
)

echo [INFO] Building frontend...
call npm run build
if errorlevel 1 (
    echo [ERROR] Frontend build failed
    exit /b 1
)

echo [INFO] Frontend build completed

:build_docker
if "%BUILD_DOCKER%"=="false" goto :complete

echo ================================
echo Building Docker Containers
echo ================================

cd /d "%PROJECT_ROOT%"

echo [INFO] Building Docker images...
docker-compose -f docker\docker-compose.yml build
if errorlevel 1 (
    echo [ERROR] Docker build failed
    exit /b 1
)

echo [INFO] Docker containers built successfully

:complete
echo ================================
echo Build Complete
echo ================================
echo [INFO] All components built successfully!

if "%INSTALL%"=="true" (
    echo [INFO] Installation directory: %INSTALL_DIR%
)

if "%BUILD_DOCKER%"=="true" (
    echo [INFO] To start the platform: docker-compose -f docker\docker-compose.yml up -d
)

echo.
echo Build Summary:
echo   Build Type: %BUILD_TYPE%
echo   Core Engine: %BUILD_CORE%
echo   Desktop App: %BUILD_DESKTOP%
echo   Backend Server: %BUILD_BACKEND%
echo   Frontend: %BUILD_FRONTEND%
echo   Docker: %BUILD_DOCKER%

cd /d "%SCRIPT_DIR%"
exit /b 0
