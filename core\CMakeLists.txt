cmake_minimum_required(VERSION 3.16)
project(ZenFlowCore VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4 /WX /permissive-)
    add_compile_definitions(_WIN32_WINNT=0x0601)
else()
    add_compile_options(-Wall -Wextra -Wpedantic -Werror)
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(-O3 -DNDEBUG)
    else()
        add_compile_options(-g -O0)
    endif()
endif()

# Find required packages
find_package(Threads REQUIRED)
find_package(PkgConfig REQUIRED)

# Find libcurl
find_package(CURL REQUIRED)
if(CURL_FOUND)
    message(STATUS "Found CURL: ${CURL_LIBRARIES}")
else()
    message(FATAL_ERROR "CURL not found")
endif()

# Find jsoncpp
pkg_check_modules(J<PERSON><PERSON><PERSON> jsoncpp)
if(NOT JSONCPP_FOUND)
    message(STATUS "jsoncpp not found via pkg-config, trying find_package")
    find_package(jsoncpp REQUIRED)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CURL_INCLUDE_DIRS})

# Core library sources
set(CORE_SOURCES
    engine/trading_engine.cpp
    market_data/market_data_handler.cpp
    orders/order_manager.cpp
    risk/risk_manager.cpp
    strategies/strategy_manager.cpp
    utils/thread_pool.cpp
    utils/logger.cpp
)

set(CORE_HEADERS
    engine/trading_engine.h
    market_data/market_data_handler.h
    orders/order_manager.h
    risk/risk_manager.h
    strategies/strategy_manager.h
    utils/thread_pool.h
    utils/logger.h
)

# Create core library
add_library(zenflow_core STATIC ${CORE_SOURCES} ${CORE_HEADERS})

# Link libraries
target_link_libraries(zenflow_core
    PRIVATE
        Threads::Threads
        ${CURL_LIBRARIES}
)

# Link jsoncpp
if(JSONCPP_FOUND)
    target_link_libraries(zenflow_core PRIVATE ${JSONCPP_LIBRARIES})
    target_include_directories(zenflow_core PRIVATE ${JSONCPP_INCLUDE_DIRS})
else()
    target_link_libraries(zenflow_core PRIVATE jsoncpp_lib)
endif()

# Set target properties
set_target_properties(zenflow_core PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    PUBLIC_HEADER "${CORE_HEADERS}"
)

# Compiler definitions
target_compile_definitions(zenflow_core PRIVATE
    ZENFLOW_VERSION_MAJOR=${PROJECT_VERSION_MAJOR}
    ZENFLOW_VERSION_MINOR=${PROJECT_VERSION_MINOR}
    ZENFLOW_VERSION_PATCH=${PROJECT_VERSION_PATCH}
)

# Enable position independent code
set_property(TARGET zenflow_core PROPERTY POSITION_INDEPENDENT_CODE ON)

# Installation
install(TARGETS zenflow_core
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    PUBLIC_HEADER DESTINATION include/zenflow
)

# Install headers with directory structure
install(DIRECTORY engine/ DESTINATION include/zenflow/engine FILES_MATCHING PATTERN "*.h")
install(DIRECTORY market_data/ DESTINATION include/zenflow/market_data FILES_MATCHING PATTERN "*.h")
install(DIRECTORY orders/ DESTINATION include/zenflow/orders FILES_MATCHING PATTERN "*.h")
install(DIRECTORY risk/ DESTINATION include/zenflow/risk FILES_MATCHING PATTERN "*.h")
install(DIRECTORY strategies/ DESTINATION include/zenflow/strategies FILES_MATCHING PATTERN "*.h")
install(DIRECTORY utils/ DESTINATION include/zenflow/utils FILES_MATCHING PATTERN "*.h")

# Create pkg-config file
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/zenflow-core.pc.in"
    "${CMAKE_CURRENT_BINARY_DIR}/zenflow-core.pc"
    @ONLY
)

install(FILES "${CMAKE_CURRENT_BINARY_DIR}/zenflow-core.pc"
    DESTINATION lib/pkgconfig
)

# Testing
if(BUILD_TESTING)
    enable_testing()
    add_subdirectory(tests)
endif()

# Examples
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# Documentation
if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        configure_file(${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)
        add_custom_target(docs
            ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# Print configuration summary
message(STATUS "ZenFlow Core Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  CURL found: ${CURL_FOUND}")
message(STATUS "  JsonCpp found: ${JSONCPP_FOUND}")
message(STATUS "  Build testing: ${BUILD_TESTING}")
message(STATUS "  Build examples: ${BUILD_EXAMPLES}")
message(STATUS "  Build docs: ${BUILD_DOCS}")
