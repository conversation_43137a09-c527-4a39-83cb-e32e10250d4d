version: '3.8'

services:
  # PostgreSQL with TimescaleDB
  database:
    image: timescale/timescaledb:latest-pg15
    container_name: zenflow_database
    environment:
      POSTGRES_DB: zenflow_trading
      POSTGRES_USER: zenflow
      POSTGRES_PASSWORD: ${DB_PASSWORD:-zenflow_secure_password}
      TIMESCALEDB_TELEMETRY: off
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database/migrations:/docker-entrypoint-initdb.d
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - zenflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U zenflow -d zenflow_trading"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: zenflow_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - zenflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ for message queuing
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: zenflow_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: zenflow
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-zenflow_rabbitmq_password}
      RABBITMQ_DEFAULT_VHOST: zenflow
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
    networks:
      - zenflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ZenFlow Backend API
  backend:
    build:
      context: ..
      dockerfile: docker/backend/Dockerfile
    container_name: zenflow_backend
    environment:
      - NODE_ENV=production
      - DB_HOST=database
      - DB_PORT=5432
      - DB_NAME=zenflow_trading
      - DB_USER=zenflow
      - DB_PASSWORD=${DB_PASSWORD:-zenflow_secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=zenflow
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD:-zenflow_rabbitmq_password}
      - JWT_SECRET=${JWT_SECRET:-zenflow_jwt_secret_change_in_production}
      - API_PORT=8080
      - WEBSOCKET_PORT=8081
      - LOG_LEVEL=INFO
    ports:
      - "8080:8080"
      - "8081:8081"
    volumes:
      - ../logs:/app/logs
      - ../config:/app/config
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - zenflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ZenFlow Frontend
  frontend:
    build:
      context: ..
      dockerfile: docker/frontend/Dockerfile
    container_name: zenflow_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - REACT_APP_WS_URL=ws://localhost:8081
      - REACT_APP_VERSION=1.0.0
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - zenflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: zenflow_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ../logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - zenflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: zenflow_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - zenflow_network
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: zenflow_grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - zenflow_network
    restart: unless-stopped

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: zenflow_node_exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - zenflow_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  zenflow_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
