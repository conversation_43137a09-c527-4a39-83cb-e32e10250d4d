https://claude.ai/chat/176d2d4b-62b9-4da5-9997-0cb3e2708dc6

Core Concept: A high-performance algorithmic trading platform that supports multiple asset classes (stocks, forex, crypto, commodities) with real-time data processing, advanced charting, portfolio management, and automated trading strategies.
Key Features:

Real-time market data streaming and processing
Advanced technical analysis with 50+ indicators
Algorithmic trading strategy builder with backtesting
Multi-asset portfolio management
Risk management tools with stop-loss automation
Social trading features (copy trading, leaderboards)
Mobile-responsive web interface + native desktop app

Development Prompts
1. Architecture & Planning Prompt
You are a senior software architect designing a high-performance trading system called "TradeFlow Pro". 

Requirements:
- Desktop app (C++ with Qt framework)
- Web app (C++ backend with WebAssembly frontend components)
- Real-time data processing (handle 100k+ ticks/second)
- Low-latency order execution (<50ms)
- Multi-asset support (stocks, forex, crypto, commodities)
- Scalable to 10k+ concurrent users

Design:
1. System architecture diagram with microservices breakdown
2. Database schema for market data, user accounts, orders, portfolios
3. API design for REST and WebSocket endpoints
4. Technology stack recommendations for each component
5. Performance optimization strategies
6. Security architecture (authentication, encryption, compliance)
7. Deployment strategy (Docker, Kubernetes, CI/CD)

Provide detailed technical specifications, code structure, and integration patterns.
2. Core Trading Engine Prompt
Build a high-performance C++ trading engine core for TradeFlow Pro with these specifications:

Components needed:
1. Market Data Handler
   - Real-time tick processing
   - Data normalization across exchanges
   - Historical data storage and retrieval
   - Market data replay for backtesting

2. Order Management System
   - Order validation and routing
   - Position tracking
   - Fill processing
   - Order book management

3. Risk Management Engine
   - Real-time P&L calculation
   - Position sizing algorithms
   - Stop-loss and take-profit automation
   - Margin and exposure monitoring

4. Strategy Engine
   - Strategy lifecycle management
   - Signal generation and processing
   - Portfolio rebalancing
   - Performance metrics calculation

Use modern C++17/20 features, implement thread-safe operations, optimize for low latency, and include comprehensive error handling. Provide complete implementation with unit tests.
3. Desktop Application Prompt
Create a professional desktop trading application using C++ and Qt6 for TradeFlow Pro:

UI Requirements:
1. Main Dashboard
   - Portfolio overview with P&L
   - Watchlist with real-time quotes
   - Active orders and positions
   - Market news feed

2. Advanced Charting Module
   - Candlestick/OHLC charts with zoom/pan
   - 50+ technical indicators overlay
   - Drawing tools (trend lines, fibonacci)
   - Multiple timeframe analysis
   - Custom indicator creation

3. Trading Interface
   - Order entry with validation
   - One-click trading buttons
   - Position management panel
   - Order history and trade journal

4. Strategy Builder
   - Drag-and-drop strategy creation
   - Code editor for custom strategies
   - Backtesting interface with results
   - Forward testing capabilities

5. Portfolio Management
   - Multi-account support
   - Asset allocation visualization
   - Performance analytics
   - Risk metrics dashboard

Implement with responsive design, dark/light themes, customizable layouts, and real-time data updates. Include keyboard shortcuts and professional styling.
4. Web Application Backend Prompt
Build a scalable C++ web backend for TradeFlow Pro trading platform:

Technology Stack:
- C++ with Crow/Drogon web framework
- PostgreSQL for transactional data
- Redis for caching and real-time data
- WebSocket for real-time communication
- JWT authentication
- Docker containerization

API Endpoints needed:
1. Authentication & User Management
   - Login/logout, 2FA support
   - Account registration and verification
   - Profile management

2. Market Data API
   - Real-time quotes and market data
   - Historical data retrieval
   - Market news and events
   - Economic calendar

3. Trading API
   - Order placement and management
   - Position tracking
   - Trade history
   - Portfolio performance

4. Strategy & Backtesting API
   - Strategy CRUD operations
   - Backtesting execution
   - Performance metrics
   - Strategy sharing

5. WebSocket Handlers
   - Real-time price feeds
   - Order status updates
   - Portfolio changes
   - System notifications

Implement proper error handling, rate limiting, logging, and monitoring. Include API documentation and comprehensive testing.
5. Frontend Web Application Prompt
Create a modern, responsive web frontend for TradeFlow Pro using a combination of WebAssembly (compiled from C++) for performance-critical components and JavaScript for UI:

Architecture:
- WebAssembly modules for charting engine and calculations
- React/Vue.js for UI components
- WebSocket client for real-time data
- Progressive Web App (PWA) capabilities
- Mobile-first responsive design

Key Components:
1. Dashboard
   - Real-time portfolio overview
   - Market summary widgets
   - Quick trade shortcuts
   - Performance charts

2. Trading Interface
   - Advanced order entry forms
   - Market depth visualization
   - Position management
   - Real-time P&L updates

3. Charting Module (WebAssembly)
   - High-performance canvas rendering
   - Technical indicators overlay
   - Drawing tools
   - Custom timeframes

4. Strategy Management
   - Strategy builder interface
   - Backtesting results visualization
   - Performance analytics
   - Social trading features

5. Portfolio Analytics
   - Asset allocation charts
   - Risk metrics display
   - Performance comparisons
   - Detailed reporting

Include offline capabilities, push notifications, and seamless mobile experience. Optimize for speed and usability.
6. Database Design Prompt
Design a comprehensive database schema for TradeFlow Pro trading system:

Requirements:
- Handle high-frequency market data (millions of records/day)
- Support multiple asset classes and exchanges
- Maintain data integrity for financial transactions
- Optimize for both read and write operations
- Include proper indexing and partitioning strategies

Tables needed:
1. User Management (users, accounts, permissions, sessions)
2. Market Data (instruments, quotes, trades, order_books)
3. Trading (orders, executions, positions, portfolios)
4. Strategies (strategies, backtests, signals, performance)
5. Risk Management (limits, exposures, alerts)
6. System (logs, configurations, notifications)

For each table, provide:
- Complete schema with constraints
- Indexing strategies
- Partitioning recommendations
- Sample queries for common operations
- Data retention policies
- Backup and recovery procedures

Consider using PostgreSQL with TimescaleDB extension for time-series data.
7. DevOps & Deployment Prompt
Create a complete DevOps pipeline for TradeFlow Pro trading system:

Infrastructure Requirements:
- Multi-environment setup (dev, staging, prod)
- High availability and disaster recovery
- Auto-scaling based on load
- Security compliance (SOC2, PCI DSS)
- Monitoring and alerting

Components:
1. CI/CD Pipeline
   - Automated testing and builds
   - Code quality checks
   - Security scanning
   - Deployment automation

2. Container Orchestration
   - Docker containerization
   - Kubernetes deployment
   - Service mesh configuration
   - Load balancing

3. Monitoring Stack
   - Application performance monitoring
   - Infrastructure monitoring
   - Log aggregation
   - Business metrics tracking

4. Security Implementation
   - Network security policies
   - Secrets management
   - SSL/TLS configuration
   - Intrusion detection

5. Backup & Recovery
   - Database backup strategies
   - Application data recovery
   - Disaster recovery procedures
   - Business continuity planning

Provide complete configuration files, scripts, and documentation for each component.

My github is https://github.com/HectorTa1989. Show me github readme with some good product names that nobody registered website domain with those names before, system architecture in mermaid syntax, workflow in mermaid syntax, Project structure all in github readme. Then code for each file in the project structure in separate artifacts (each file in 1 block) with exact file path, file name. Write commit message for each file after each file, so I can commit to github. Code using our own algorithms and free APIs is better