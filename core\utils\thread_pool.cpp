#include "thread_pool.h"

namespace zenflow {

ThreadPool::ThreadPool(size_t num_threads) {
    if (num_threads == 0) {
        num_threads = std::thread::hardware_concurrency();
        if (num_threads == 0) {
            num_threads = 4; // Fallback to 4 threads
        }
    }

    workers_.reserve(num_threads);
    
    for (size_t i = 0; i < num_threads; ++i) {
        workers_.emplace_back([this] {
            while (true) {
                std::function<void()> task;

                {
                    std::unique_lock<std::mutex> lock(queue_mutex_);
                    
                    // Wait for a task or stop signal
                    condition_.wait(lock, [this] {
                        return stop_.load() || !tasks_.empty();
                    });
                    
                    // Exit if stopped and no more tasks
                    if (stop_.load() && tasks_.empty()) {
                        return;
                    }
                    
                    // Get the next task
                    if (!tasks_.empty()) {
                        task = std::move(tasks_.front());
                        tasks_.pop();
                        pending_tasks_.fetch_sub(1);
                        active_threads_.fetch_add(1);
                    }
                }

                // Execute the task
                if (task) {
                    try {
                        task();
                    } catch (const std::exception& e) {
                        // Log error in production code
                        // For now, just continue
                    } catch (...) {
                        // Handle unknown exceptions
                    }
                    
                    active_threads_.fetch_sub(1);
                    
                    // Notify if all tasks are done
                    {
                        std::lock_guard<std::mutex> lock(queue_mutex_);
                        if (tasks_.empty() && active_threads_.load() == 0) {
                            finished_condition_.notify_all();
                        }
                    }
                }
            }
        });
    }
}

ThreadPool::~ThreadPool() {
    Shutdown();
}

size_t ThreadPool::GetPendingTaskCount() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return tasks_.size();
}

void ThreadPool::WaitForAll() {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    finished_condition_.wait(lock, [this] {
        return tasks_.empty() && active_threads_.load() == 0;
    });
}

void ThreadPool::Shutdown() {
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        stop_.store(true);
    }
    
    condition_.notify_all();
    
    for (std::thread& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
    
    workers_.clear();
}

} // namespace zenflow
