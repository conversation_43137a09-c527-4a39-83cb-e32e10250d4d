#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <atomic>
#include <mutex>
#include <functional>
#include "../orders/order_manager.h"
#include "../market_data/market_data_handler.h"

namespace zenflow {

enum class RiskCheckType {
    PRE_TRADE,
    POST_TRADE,
    REAL_TIME
};

struct RiskLimit {
    std::string symbol;
    double max_position_size = 0;
    double max_order_size = 0;
    double max_daily_loss = 0;
    double max_daily_volume = 0;
    double max_concentration = 0.1; // 10% of portfolio
    bool enabled = true;
    
    bool IsValid() const {
        return !symbol.empty() && max_position_size > 0;
    }
};

struct RiskMetrics {
    std::string symbol;
    double current_position = 0;
    double daily_pnl = 0;
    double daily_volume = 0;
    double var_95 = 0;        // Value at Risk 95%
    double max_drawdown = 0;
    double sharpe_ratio = 0;
    double beta = 0;
    int64_t last_updated = 0;
    
    std::string ToString() const {
        return symbol + " Pos:" + std::to_string(current_position) + 
               " PnL:" + std::to_string(daily_pnl) + 
               " VaR:" + std::to_string(var_95);
    }
};

struct RiskValidationResult {
    bool approved = false;
    std::string reason;
    RiskCheckType check_type;
    double risk_score = 0;
    std::vector<std::string> warnings;
    
    std::string ToString() const {
        return approved ? "APPROVED" : "REJECTED: " + reason;
    }
};

enum class RiskEventType {
    LIMIT_BREACH,
    WARNING_THRESHOLD,
    POSITION_LIMIT,
    LOSS_LIMIT,
    CONCENTRATION_LIMIT,
    VOLATILITY_SPIKE
};

struct RiskEvent {
    RiskEventType type;
    std::string symbol;
    std::string message;
    double current_value;
    double limit_value;
    int64_t timestamp;
    
    std::string ToString() const {
        return symbol + " " + message + " (" + 
               std::to_string(current_value) + "/" + std::to_string(limit_value) + ")";
    }
};

struct PortfolioRisk {
    double total_exposure = 0;
    double net_exposure = 0;
    double gross_exposure = 0;
    double leverage = 0;
    double portfolio_var = 0;
    double max_drawdown = 0;
    double sharpe_ratio = 0;
    double sortino_ratio = 0;
    std::unordered_map<std::string, double> sector_exposure;
    std::unordered_map<std::string, double> currency_exposure;
    int64_t last_updated = 0;
};

class RiskManager {
public:
    RiskManager();
    ~RiskManager();

    // Lifecycle management
    bool Initialize();
    bool Start();
    bool Stop();
    bool IsRunning() const { return is_running_.load(); }

    // Risk limit management
    bool SetRiskLimit(const std::string& symbol, const RiskLimit& limit);
    bool RemoveRiskLimit(const std::string& symbol);
    RiskLimit GetRiskLimit(const std::string& symbol) const;
    std::vector<RiskLimit> GetAllRiskLimits() const;

    // Risk validation
    RiskValidationResult ValidateOrder(const OrderRequest& request);
    RiskValidationResult ValidatePosition(const std::string& symbol, double new_quantity);
    RiskValidationResult ValidatePortfolio();

    // Risk monitoring
    void OnMarketData(const MarketData& data);
    void OnOrderEvent(const OrderEvent& event);
    void OnPositionEvent(const PositionEvent& event);
    void OnFill(const Fill& fill);

    // Risk metrics
    RiskMetrics GetRiskMetrics(const std::string& symbol) const;
    std::vector<RiskMetrics> GetAllRiskMetrics() const;
    PortfolioRisk GetPortfolioRisk() const;

    // Risk controls
    bool EnableRiskChecks(bool enable);
    bool IsRiskChecksEnabled() const { return risk_checks_enabled_.load(); }
    bool SetEmergencyStop(bool enable);
    bool IsEmergencyStopActive() const { return emergency_stop_.load(); }

    // Callbacks
    void SetRiskEventCallback(std::function<void(const RiskEvent&)> callback);

    // Configuration
    void SetMaxPortfolioLoss(double max_loss);
    void SetMaxLeverage(double max_leverage);
    void SetVarConfidenceLevel(double confidence);

private:
    // Risk limits storage
    std::unordered_map<std::string, RiskLimit> risk_limits_;
    mutable std::mutex limits_mutex_;

    // Risk metrics storage
    std::unordered_map<std::string, RiskMetrics> risk_metrics_;
    mutable std::mutex metrics_mutex_;

    // Portfolio risk
    PortfolioRisk portfolio_risk_;
    mutable std::mutex portfolio_mutex_;

    // Market data cache
    std::unordered_map<std::string, MarketData> market_data_cache_;
    mutable std::mutex market_data_mutex_;

    // State management
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_initialized_{false};
    std::atomic<bool> risk_checks_enabled_{true};
    std::atomic<bool> emergency_stop_{false};

    // Configuration
    double max_portfolio_loss_ = -100000.0; // $100k max loss
    double max_leverage_ = 3.0;
    double var_confidence_level_ = 0.95;

    // Callbacks
    std::function<void(const RiskEvent&)> risk_event_callback_;

    // Internal methods
    bool ValidatePositionSize(const std::string& symbol, double quantity);
    bool ValidateOrderSize(const std::string& symbol, double quantity);
    bool ValidateDailyLoss(const std::string& symbol);
    bool ValidateConcentration(const std::string& symbol, double quantity);
    bool ValidatePortfolioLimits();

    void UpdateRiskMetrics(const std::string& symbol);
    void UpdatePortfolioRisk();
    void CalculateVaR(const std::string& symbol);
    void CalculatePortfolioVaR();

    void EmitRiskEvent(RiskEventType type, const std::string& symbol, 
                      const std::string& message, double current_value, double limit_value);

    // Helper methods
    double GetCurrentPrice(const std::string& symbol) const;
    double GetPositionValue(const std::string& symbol) const;
    double GetPortfolioValue() const;
    int64_t GetCurrentTimestamp() const;

    // Statistical calculations
    double CalculateSharpeRatio(const std::vector<double>& returns) const;
    double CalculateMaxDrawdown(const std::vector<double>& values) const;
    double CalculateVolatility(const std::vector<double>& returns) const;
    std::vector<double> GetReturns(const std::string& symbol, int periods = 252) const;
};

} // namespace zenflow
