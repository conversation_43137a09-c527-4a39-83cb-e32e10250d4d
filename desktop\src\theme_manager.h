#pragma once

#include <QObject>
#include <QString>
#include <QApplication>
#include <QPalette>

class ThemeManager : public QObject {
    Q_OBJECT

public:
    explicit ThemeManager(QObject* parent = nullptr);
    ~ThemeManager() = default;

    enum class Theme {
        Light,
        Dark,
        System
    };

    void ApplyTheme(const QString& themeName);
    void ApplyTheme(Theme theme);
    
    QString GetCurrentTheme() const;
    QStringList GetAvailableThemes() const;
    
    bool IsDarkTheme() const;

signals:
    void ThemeChanged(const QString& themeName);

private:
    void ApplyLightTheme();
    void ApplyDarkTheme();
    void ApplySystemTheme();
    
    QString GetStyleSheet(Theme theme) const;
    QString GetDarkStyleSheet() const;
    QString GetLightStyleSheet() const;
    
    Theme current_theme_;
    QString current_theme_name_;
};

#endif // THEME_MANAGER_H
