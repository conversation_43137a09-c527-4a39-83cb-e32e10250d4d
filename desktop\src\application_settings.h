#pragma once

#include <QObject>
#include <QSettings>
#include <QString>
#include <QStringList>

class ApplicationSettings : public QObject {
    Q_OBJECT

public:
    explicit ApplicationSettings(QObject* parent = nullptr);
    ~ApplicationSettings() = default;

    // Theme settings
    QString GetTheme() const;
    void SetTheme(const QString& theme);

    // Trading settings
    double GetDefaultQuantity() const;
    void SetDefaultQuantity(double quantity);
    
    QString GetDefaultOrderType() const;
    void SetDefaultOrderType(const QString& type);
    
    QString GetDefaultTimeInForce() const;
    void SetDefaultTimeInForce(const QString& tif);

    // UI settings
    bool GetAutoSaveWorkspace() const;
    void SetAutoSaveWorkspace(bool enabled);
    
    int GetRefreshInterval() const;
    void SetRefreshInterval(int interval);
    
    bool GetShowSystemTray() const;
    void SetShowSystemTray(bool show);

    // Watchlist settings
    QStringList GetWatchlistSymbols() const;
    void SetWatchlistSymbols(const QStringList& symbols);
    
    // Risk settings
    double GetMaxOrderSize() const;
    void SetMaxOrderSize(double size);
    
    double GetMaxDailyLoss() const;
    void SetMaxDailyLoss(double loss);

    // Save/Load
    void Save();
    void Load();
    void Reset();

signals:
    void SettingsChanged();
    void ThemeChanged(const QString& theme);

private:
    QSettings* settings_;
    
    // Default values
    static constexpr double DEFAULT_QUANTITY = 100.0;
    static constexpr double DEFAULT_MAX_ORDER_SIZE = 10000.0;
    static constexpr double DEFAULT_MAX_DAILY_LOSS = 5000.0;
    static constexpr int DEFAULT_REFRESH_INTERVAL = 1000;
};

#endif // APPLICATION_SETTINGS_H
