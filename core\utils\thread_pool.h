#pragma once

#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <stdexcept>
#include <atomic>

namespace zenflow {

class ThreadPool {
public:
    explicit ThreadPool(size_t num_threads = std::thread::hardware_concurrency());
    ~ThreadPool();

    // Submit a task to the thread pool
    template<class F, class... Args>
    auto Submit(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type>;

    // Get the number of threads in the pool
    size_t GetThreadCount() const { return workers_.size(); }
    
    // Get the number of pending tasks
    size_t GetPendingTaskCount() const;
    
    // Get the number of active threads
    size_t GetActiveThreadCount() const { return active_threads_.load(); }
    
    // Check if the thread pool is running
    bool IsRunning() const { return !stop_.load(); }
    
    // Wait for all tasks to complete
    void WaitForAll();
    
    // Shutdown the thread pool gracefully
    void Shutdown();

private:
    // Worker threads
    std::vector<std::thread> workers_;
    
    // Task queue
    std::queue<std::function<void()>> tasks_;
    
    // Synchronization
    std::mutex queue_mutex_;
    std::condition_variable condition_;
    std::condition_variable finished_condition_;
    
    // State
    std::atomic<bool> stop_{false};
    std::atomic<size_t> active_threads_{0};
    std::atomic<size_t> pending_tasks_{0};
};

// Template implementation
template<class F, class... Args>
auto ThreadPool::Submit(F&& f, Args&&... args) 
    -> std::future<typename std::result_of<F(Args...)>::type> {
    
    using return_type = typename std::result_of<F(Args...)>::type;

    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...)
    );

    std::future<return_type> result = task->get_future();
    
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);

        // Don't allow enqueueing after stopping the pool
        if (stop_.load()) {
            throw std::runtime_error("Cannot submit task to stopped ThreadPool");
        }

        tasks_.emplace([task](){ (*task)(); });
        pending_tasks_.fetch_add(1);
    }
    
    condition_.notify_one();
    return result;
}

} // namespace zenflow
