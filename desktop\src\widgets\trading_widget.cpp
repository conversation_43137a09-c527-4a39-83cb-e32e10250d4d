#include "trading_widget.h"
#include <QMessageBox>
#include <QHeaderView>
#include <QApplication>
#include <QDateTime>
#include <QDialog>
#include <QDialogButtonBox>
#include <chrono>

TradingWidget::TradingWidget(QWidget* parent)
    : QWidget(parent)
    , trading_engine_(nullptr)
    , current_symbol_("AAPL")
    , last_price_(0.0)
    , bid_price_(0.0)
    , ask_price_(0.0)
    , current_position_(0.0)
{
    SetupUI();
    
    // Initialize timers
    market_data_timer_ = new QTimer(this);
    order_update_timer_ = new QTimer(this);
    
    connect(market_data_timer_, &QTimer::timeout, this, &TradingWidget::UpdateMarketData);
    connect(order_update_timer_, &QTimer::timeout, this, &TradingWidget::UpdateActiveOrders);
    
    market_data_timer_->start(MARKET_DATA_UPDATE_INTERVAL);
    order_update_timer_->start(ORDER_UPDATE_INTERVAL);
    
    // Set default values
    symbol_combo_->setCurrentText(current_symbol_);
    quantity_spin_->setValue(100);
    time_in_force_combo_->setCurrentText("DAY");
    market_radio_->setChecked(true);
    
    OnOrderTypeChanged();
    ValidateOrder();
}

void TradingWidget::SetupUI() {
    main_layout_ = new QVBoxLayout(this);
    main_layout_->setSpacing(10);
    main_layout_->setContentsMargins(10, 10, 10, 10);
    
    SetupOrderEntry();
    SetupOrderBook();
    SetupPositions();
    SetupActiveOrders();
    
    setLayout(main_layout_);
}

void TradingWidget::SetupOrderEntry() {
    order_entry_group_ = new QGroupBox("Order Entry", this);
    order_entry_layout_ = new QGridLayout(order_entry_group_);
    
    // Symbol selection
    order_entry_layout_->addWidget(new QLabel("Symbol:"), 0, 0);
    symbol_combo_ = new QComboBox();
    symbol_combo_->setEditable(true);
    symbol_combo_->addItems({"AAPL", "GOOGL", "MSFT", "AMZN", "TSLA", "META", "NVDA", "NFLX"});
    order_entry_layout_->addWidget(symbol_combo_, 0, 1);
    
    // Order type
    order_entry_layout_->addWidget(new QLabel("Order Type:"), 1, 0);
    order_type_group_ = new QButtonGroup(this);
    
    QHBoxLayout* order_type_layout = new QHBoxLayout();
    market_radio_ = new QRadioButton("Market");
    limit_radio_ = new QRadioButton("Limit");
    stop_radio_ = new QRadioButton("Stop");
    stop_limit_radio_ = new QRadioButton("Stop Limit");
    
    order_type_group_->addButton(market_radio_, static_cast<int>(zenflow::OrderType::MARKET));
    order_type_group_->addButton(limit_radio_, static_cast<int>(zenflow::OrderType::LIMIT));
    order_type_group_->addButton(stop_radio_, static_cast<int>(zenflow::OrderType::STOP));
    order_type_group_->addButton(stop_limit_radio_, static_cast<int>(zenflow::OrderType::STOP_LIMIT));
    
    order_type_layout->addWidget(market_radio_);
    order_type_layout->addWidget(limit_radio_);
    order_type_layout->addWidget(stop_radio_);
    order_type_layout->addWidget(stop_limit_radio_);
    order_entry_layout_->addLayout(order_type_layout, 1, 1);
    
    // Quantity
    order_entry_layout_->addWidget(new QLabel("Quantity:"), 2, 0);
    quantity_spin_ = new QDoubleSpinBox();
    quantity_spin_->setRange(1, 1000000);
    quantity_spin_->setDecimals(0);
    quantity_spin_->setSuffix(" shares");
    order_entry_layout_->addWidget(quantity_spin_, 2, 1);
    
    // Price
    order_entry_layout_->addWidget(new QLabel("Price:"), 3, 0);
    price_spin_ = new QDoubleSpinBox();
    price_spin_->setRange(0.01, 10000.00);
    price_spin_->setDecimals(2);
    price_spin_->setPrefix("$");
    order_entry_layout_->addWidget(price_spin_, 3, 1);
    
    // Stop price
    order_entry_layout_->addWidget(new QLabel("Stop Price:"), 4, 0);
    stop_price_spin_ = new QDoubleSpinBox();
    stop_price_spin_->setRange(0.01, 10000.00);
    stop_price_spin_->setDecimals(2);
    stop_price_spin_->setPrefix("$");
    order_entry_layout_->addWidget(stop_price_spin_, 4, 1);
    
    // Time in force
    order_entry_layout_->addWidget(new QLabel("Time in Force:"), 5, 0);
    time_in_force_combo_ = new QComboBox();
    time_in_force_combo_->addItems({"DAY", "GTC", "IOC", "FOK"});
    order_entry_layout_->addWidget(time_in_force_combo_, 5, 1);
    
    // Market data display
    last_price_label_ = new QLabel("Last: $0.00");
    last_price_label_->setStyleSheet("font-weight: bold; color: blue;");
    order_entry_layout_->addWidget(last_price_label_, 6, 0);
    
    bid_ask_label_ = new QLabel("Bid: $0.00 / Ask: $0.00");
    order_entry_layout_->addWidget(bid_ask_label_, 6, 1);
    
    position_label_ = new QLabel("Position: 0 shares");
    position_label_->setStyleSheet("font-weight: bold;");
    order_entry_layout_->addWidget(position_label_, 7, 0, 1, 2);
    
    // Buy/Sell buttons
    QHBoxLayout* button_layout = new QHBoxLayout();
    buy_button_ = new QPushButton("BUY");
    buy_button_->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }");
    sell_button_ = new QPushButton("SELL");
    sell_button_->setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 10px; }");
    
    button_layout->addWidget(buy_button_);
    button_layout->addWidget(sell_button_);
    order_entry_layout_->addLayout(button_layout, 8, 0, 1, 2);
    
    main_layout_->addWidget(order_entry_group_);
    
    // Connect signals
    connect(symbol_combo_, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &TradingWidget::OnSymbolChanged);
    connect(order_type_group_, QOverload<int>::of(&QButtonGroup::buttonClicked),
            this, &TradingWidget::OnOrderTypeChanged);
    connect(quantity_spin_, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TradingWidget::OnQuantityChanged);
    connect(price_spin_, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &TradingWidget::OnPriceChanged);
    connect(buy_button_, &QPushButton::clicked, this, &TradingWidget::OnBuyClicked);
    connect(sell_button_, &QPushButton::clicked, this, &TradingWidget::OnSellClicked);
}

void TradingWidget::SetupOrderBook() {
    order_book_group_ = new QGroupBox("Order Book", this);
    order_book_layout_ = new QVBoxLayout(order_book_group_);
    
    order_book_table_ = new QTableWidget(0, 3);
    order_book_table_->setHorizontalHeaderLabels({"Price", "Size", "Side"});
    order_book_table_->horizontalHeader()->setStretchLastSection(true);
    order_book_table_->setAlternatingRowColors(true);
    order_book_table_->setSelectionBehavior(QAbstractItemView::SelectRows);
    order_book_table_->setMaximumHeight(200);
    
    order_book_layout_->addWidget(order_book_table_);
    main_layout_->addWidget(order_book_group_);
}

void TradingWidget::SetupPositions() {
    positions_group_ = new QGroupBox("Positions", this);
    positions_layout_ = new QVBoxLayout(positions_group_);
    
    positions_table_ = new QTableWidget(0, 5);
    positions_table_->setHorizontalHeaderLabels({"Symbol", "Quantity", "Avg Price", "Market Value", "P&L"});
    positions_table_->horizontalHeader()->setStretchLastSection(true);
    positions_table_->setAlternatingRowColors(true);
    positions_table_->setSelectionBehavior(QAbstractItemView::SelectRows);
    positions_table_->setMaximumHeight(150);
    
    positions_layout_->addWidget(positions_table_);
    main_layout_->addWidget(positions_group_);
}

void TradingWidget::SetupActiveOrders() {
    active_orders_group_ = new QGroupBox("Active Orders", this);
    active_orders_layout_ = new QVBoxLayout(active_orders_group_);
    
    active_orders_table_ = new QTableWidget(0, 7);
    active_orders_table_->setHorizontalHeaderLabels({
        "Order ID", "Symbol", "Side", "Type", "Quantity", "Price", "Status"
    });
    active_orders_table_->horizontalHeader()->setStretchLastSection(true);
    active_orders_table_->setAlternatingRowColors(true);
    active_orders_table_->setSelectionBehavior(QAbstractItemView::SelectRows);
    active_orders_table_->setMaximumHeight(150);
    
    cancel_all_button_ = new QPushButton("Cancel All Orders");
    cancel_all_button_->setStyleSheet("QPushButton { background-color: #ff9800; color: white; }");
    
    active_orders_layout_->addWidget(active_orders_table_);
    active_orders_layout_->addWidget(cancel_all_button_);
    main_layout_->addWidget(active_orders_group_);
    
    connect(cancel_all_button_, &QPushButton::clicked, this, &TradingWidget::OnCancelAllClicked);
    connect(active_orders_table_, &QTableWidget::cellDoubleClicked, 
            this, &TradingWidget::OnCancelOrderClicked);
}

void TradingWidget::SetTradingEngine(zenflow::TradingEngine* engine) {
    trading_engine_ = engine;
    UpdatePositions();
    UpdateActiveOrders();
}

void TradingWidget::QuickBuy(const QString& symbol) {
    QuickTradeDialog dialog(symbol, zenflow::OrderSide::BUY, this);
    if (dialog.exec() == QDialog::Accepted) {
        // Place quick buy order
        zenflow::OrderRequest request;
        request.symbol = dialog.GetSymbol().toStdString();
        request.type = dialog.GetOrderType();
        request.side = zenflow::OrderSide::BUY;
        request.quantity = dialog.GetQuantity();
        request.price = dialog.GetPrice();
        request.strategy_id = "quick_trade";
        
        if (trading_engine_) {
            auto order_id = trading_engine_->PlaceOrder(request);
            if (order_id != zenflow::INVALID_ORDER_ID) {
                emit OrderPlaced(dialog.GetSymbol(), dialog.GetQuantity(), dialog.GetPrice());
            }
        }
    }
}

void TradingWidget::QuickSell(const QString& symbol) {
    QuickTradeDialog dialog(symbol, zenflow::OrderSide::SELL, this);
    if (dialog.exec() == QDialog::Accepted) {
        // Place quick sell order
        zenflow::OrderRequest request;
        request.symbol = dialog.GetSymbol().toStdString();
        request.type = dialog.GetOrderType();
        request.side = zenflow::OrderSide::SELL;
        request.quantity = dialog.GetQuantity();
        request.price = dialog.GetPrice();
        request.strategy_id = "quick_trade";
        
        if (trading_engine_) {
            auto order_id = trading_engine_->PlaceOrder(request);
            if (order_id != zenflow::INVALID_ORDER_ID) {
                emit OrderPlaced(dialog.GetSymbol(), dialog.GetQuantity(), dialog.GetPrice());
            }
        }
    }
}

void TradingWidget::OnSymbolChanged() {
    current_symbol_ = symbol_combo_->currentText();
    UpdateMarketData();
    UpdatePositions();
}

void TradingWidget::OnOrderTypeChanged() {
    auto order_type = static_cast<zenflow::OrderType>(order_type_group_->checkedId());
    
    // Enable/disable price fields based on order type
    switch (order_type) {
        case zenflow::OrderType::MARKET:
            price_spin_->setEnabled(false);
            stop_price_spin_->setEnabled(false);
            break;
        case zenflow::OrderType::LIMIT:
            price_spin_->setEnabled(true);
            stop_price_spin_->setEnabled(false);
            break;
        case zenflow::OrderType::STOP:
            price_spin_->setEnabled(false);
            stop_price_spin_->setEnabled(true);
            break;
        case zenflow::OrderType::STOP_LIMIT:
            price_spin_->setEnabled(true);
            stop_price_spin_->setEnabled(true);
            break;
    }
    
    ValidateOrder();
}

void TradingWidget::OnBuyClicked() {
    PlaceOrder(zenflow::OrderSide::BUY);
}

void TradingWidget::OnSellClicked() {
    PlaceOrder(zenflow::OrderSide::SELL);
}

void TradingWidget::OnQuantityChanged() {
    ValidateOrder();
}

void TradingWidget::OnPriceChanged() {
    ValidateOrder();
}

void TradingWidget::ValidateOrder() {
    auto order_type = static_cast<zenflow::OrderType>(order_type_group_->checkedId());
    
    auto result = OrderValidator::ValidateOrder(
        current_symbol_,
        order_type,
        zenflow::OrderSide::BUY, // Side doesn't matter for validation
        quantity_spin_->value(),
        price_spin_->value(),
        stop_price_spin_->value()
    );
    
    buy_button_->setEnabled(result.is_valid);
    sell_button_->setEnabled(result.is_valid);
    
    if (!result.error_message.isEmpty()) {
        buy_button_->setToolTip(result.error_message);
        sell_button_->setToolTip(result.error_message);
    } else {
        buy_button_->setToolTip("");
        sell_button_->setToolTip("");
    }
}

void TradingWidget::PlaceOrder(zenflow::OrderSide side) {
    if (!trading_engine_) {
        QMessageBox::warning(this, "Error", "Trading engine not available");
        return;
    }

    auto order_type = static_cast<zenflow::OrderType>(order_type_group_->checkedId());

    zenflow::OrderRequest request;
    request.symbol = current_symbol_.toStdString();
    request.type = order_type;
    request.side = side;
    request.quantity = quantity_spin_->value();
    request.price = price_spin_->value();
    request.stop_price = stop_price_spin_->value();
    request.strategy_id = "manual";

    // Convert time in force
    QString tif = time_in_force_combo_->currentText();
    if (tif == "DAY") request.time_in_force = zenflow::TimeInForce::DAY;
    else if (tif == "GTC") request.time_in_force = zenflow::TimeInForce::GTC;
    else if (tif == "IOC") request.time_in_force = zenflow::TimeInForce::IOC;
    else if (tif == "FOK") request.time_in_force = zenflow::TimeInForce::FOK;

    try {
        auto order_id = trading_engine_->PlaceOrder(request);
        if (order_id != zenflow::INVALID_ORDER_ID) {
            emit OrderPlaced(current_symbol_, request.quantity, request.price);

            // Reset form for market orders
            if (order_type == zenflow::OrderType::MARKET) {
                quantity_spin_->setValue(100);
            }
        } else {
            QMessageBox::warning(this, "Order Failed", "Failed to place order. Check logs for details.");
        }
    } catch (const std::exception& e) {
        QMessageBox::critical(this, "Order Error", QString("Order error: %1").arg(e.what()));
    }
}

void TradingWidget::UpdateMarketData() {
    // Simulate market data updates
    // In a real implementation, this would get data from the trading engine
    static double base_price = 150.0;
    static double volatility = 0.02;

    // Simple random walk
    double change = (qrand() / double(RAND_MAX) - 0.5) * volatility * base_price;
    last_price_ = base_price + change;
    bid_price_ = last_price_ - 0.01;
    ask_price_ = last_price_ + 0.01;

    // Update labels
    last_price_label_->setText(QString("Last: $%1").arg(last_price_, 0, 'f', 2));
    bid_ask_label_->setText(QString("Bid: $%1 / Ask: $%2")
        .arg(bid_price_, 0, 'f', 2).arg(ask_price_, 0, 'f', 2));

    // Update price spinbox default values
    if (price_spin_->value() == 0.0) {
        price_spin_->setValue(last_price_);
    }
    if (stop_price_spin_->value() == 0.0) {
        stop_price_spin_->setValue(last_price_);
    }
}

void TradingWidget::UpdatePositions() {
    if (!trading_engine_) {
        return;
    }

    auto positions = trading_engine_->GetAllPositions();
    positions_table_->setRowCount(positions.size());

    for (size_t i = 0; i < positions.size(); ++i) {
        const auto& pos = positions[i];

        positions_table_->setItem(i, 0, new QTableWidgetItem(QString::fromStdString(pos.symbol)));
        positions_table_->setItem(i, 1, new QTableWidgetItem(QString::number(pos.quantity, 'f', 0)));
        positions_table_->setItem(i, 2, new QTableWidgetItem(QString::number(pos.average_price, 'f', 2)));

        double market_value = pos.quantity * last_price_; // Simplified
        positions_table_->setItem(i, 3, new QTableWidgetItem(QString::number(market_value, 'f', 2)));

        double total_pnl = pos.realized_pnl + pos.unrealized_pnl;
        auto pnl_item = new QTableWidgetItem(QString::number(total_pnl, 'f', 2));
        pnl_item->setForeground(total_pnl >= 0 ? Qt::green : Qt::red);
        positions_table_->setItem(i, 4, pnl_item);

        // Update current position for selected symbol
        if (QString::fromStdString(pos.symbol) == current_symbol_) {
            current_position_ = pos.quantity;
            position_label_->setText(QString("Position: %1 shares").arg(pos.quantity, 0, 'f', 0));
            position_label_->setStyleSheet(pos.quantity > 0 ? "color: green; font-weight: bold;" :
                                         pos.quantity < 0 ? "color: red; font-weight: bold;" :
                                         "color: black; font-weight: bold;");
        }
    }
}

void TradingWidget::UpdateActiveOrders() {
    if (!trading_engine_) {
        return;
    }

    auto orders = trading_engine_->GetActiveOrders();
    active_orders_table_->setRowCount(orders.size());

    for (size_t i = 0; i < orders.size(); ++i) {
        const auto& order = orders[i];

        active_orders_table_->setItem(i, 0, new QTableWidgetItem(QString::number(order.id)));
        active_orders_table_->setItem(i, 1, new QTableWidgetItem(QString::fromStdString(order.symbol)));

        QString side_str = (order.side == zenflow::OrderSide::BUY) ? "BUY" : "SELL";
        auto side_item = new QTableWidgetItem(side_str);
        side_item->setForeground((order.side == zenflow::OrderSide::BUY) ? Qt::green : Qt::red);
        active_orders_table_->setItem(i, 2, side_item);

        QString type_str;
        switch (order.type) {
            case zenflow::OrderType::MARKET: type_str = "MARKET"; break;
            case zenflow::OrderType::LIMIT: type_str = "LIMIT"; break;
            case zenflow::OrderType::STOP: type_str = "STOP"; break;
            case zenflow::OrderType::STOP_LIMIT: type_str = "STOP_LIMIT"; break;
        }
        active_orders_table_->setItem(i, 3, new QTableWidgetItem(type_str));

        active_orders_table_->setItem(i, 4, new QTableWidgetItem(QString::number(order.quantity, 'f', 0)));
        active_orders_table_->setItem(i, 5, new QTableWidgetItem(QString::number(order.price, 'f', 2)));

        QString status_str;
        switch (order.status) {
            case zenflow::OrderStatus::PENDING: status_str = "PENDING"; break;
            case zenflow::OrderStatus::SUBMITTED: status_str = "SUBMITTED"; break;
            case zenflow::OrderStatus::PARTIALLY_FILLED: status_str = "PARTIAL"; break;
            case zenflow::OrderStatus::FILLED: status_str = "FILLED"; break;
            case zenflow::OrderStatus::CANCELLED: status_str = "CANCELLED"; break;
            case zenflow::OrderStatus::REJECTED: status_str = "REJECTED"; break;
        }
        active_orders_table_->setItem(i, 6, new QTableWidgetItem(status_str));
    }
}

void TradingWidget::OnCancelOrderClicked() {
    int row = active_orders_table_->currentRow();
    if (row >= 0) {
        auto order_id_item = active_orders_table_->item(row, 0);
        if (order_id_item) {
            int order_id = order_id_item->text().toInt();
            if (trading_engine_ && trading_engine_->CancelOrder(order_id)) {
                emit OrderCancelled(order_id);
            }
        }
    }
}

void TradingWidget::OnCancelAllClicked() {
    if (!trading_engine_) {
        return;
    }

    int ret = QMessageBox::question(this, "Cancel All Orders",
        "Are you sure you want to cancel all active orders?",
        QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        auto orders = trading_engine_->GetActiveOrders();
        for (const auto& order : orders) {
            trading_engine_->CancelOrder(order.id);
        }
    }
}

// RateLimiter implementation
RateLimiter::RateLimiter(int max_requests_per_second, int burst_capacity)
    : max_requests_per_second_(max_requests_per_second)
    , burst_capacity_(burst_capacity)
    , current_count_(0)
    , last_reset_(std::chrono::steady_clock::now())
    , last_request_(std::chrono::steady_clock::now())
{
}

bool RateLimiter::AllowRequest() {
    UpdateCounts();

    if (current_count_ < burst_capacity_) {
        current_count_++;
        last_request_ = std::chrono::steady_clock::now();
        return true;
    }

    return false;
}

void RateLimiter::Reset() {
    current_count_ = 0;
    last_reset_ = std::chrono::steady_clock::now();
}

void RateLimiter::UpdateCounts() {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_reset_).count();

    if (elapsed >= 1000) { // 1 second
        current_count_ = std::max(0, current_count_ - max_requests_per_second_);
        last_reset_ = now;
    }
}

// OrderValidator implementation
OrderValidator::ValidationResult OrderValidator::ValidateOrder(
    const QString& symbol,
    zenflow::OrderType type,
    zenflow::OrderSide side,
    double quantity,
    double price,
    double stop_price)
{
    ValidationResult result;

    if (!IsValidSymbol(symbol)) {
        result.error_message = "Invalid symbol";
        return result;
    }

    if (!IsValidQuantity(quantity)) {
        result.error_message = "Invalid quantity";
        return result;
    }

    if (type != zenflow::OrderType::MARKET && !IsValidPrice(price)) {
        result.error_message = "Invalid price";
        return result;
    }

    if ((type == zenflow::OrderType::STOP || type == zenflow::OrderType::STOP_LIMIT) &&
        !IsValidPrice(stop_price)) {
        result.error_message = "Invalid stop price";
        return result;
    }

    if (!IsMarketOpen()) {
        result.warning_message = "Market is currently closed";
    }

    result.is_valid = true;
    return result;
}

bool OrderValidator::IsValidSymbol(const QString& symbol) {
    return !symbol.isEmpty() && symbol.length() <= 10;
}

bool OrderValidator::IsValidQuantity(double quantity) {
    return quantity > 0 && quantity <= 1000000;
}

bool OrderValidator::IsValidPrice(double price) {
    return price > 0 && price <= 10000;
}

bool OrderValidator::IsMarketOpen() {
    QTime now = QTime::currentTime();
    return now >= QTime(9, 30) && now <= QTime(16, 0);
}

#include "trading_widget.moc"
