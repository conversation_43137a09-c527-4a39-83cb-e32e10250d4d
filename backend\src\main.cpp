#include <iostream>
#include <memory>
#include <signal.h>
#include <thread>
#include <chrono>

#include "api_server.h"
#include "websocket_server.h"
#include "database_manager.h"
#include "config_manager.h"
#include "../../core/engine/trading_engine.h"
#include "../../core/utils/logger.h"

class ZenFlowBackend {
public:
    ZenFlowBackend() = default;
    ~ZenFlowBackend() = default;

    bool Initialize() {
        try {
            // Initialize logging
            InitializeLogging();
            LOG_INFO("ZenFlow Backend starting up...");

            // Load configuration
            config_manager_ = std::make_unique<ConfigManager>("config/server.json");
            if (!config_manager_->Load()) {
                LOG_ERROR("Failed to load configuration");
                return false;
            }

            // Initialize database
            database_manager_ = std::make_unique<DatabaseManager>(
                config_manager_->GetDatabaseConfig());
            if (!database_manager_->Initialize()) {
                LOG_ERROR("Failed to initialize database");
                return false;
            }

            // Initialize trading engine
            zenflow::EngineConfig engine_config;
            engine_config.enable_logging = true;
            engine_config.enable_risk_management = true;
            engine_config.max_threads = config_manager_->GetMaxThreads();
            
            trading_engine_ = zenflow::CreateTradingEngine(engine_config);
            if (!trading_engine_->Initialize()) {
                LOG_ERROR("Failed to initialize trading engine");
                return false;
            }

            // Initialize API server
            api_server_ = std::make_unique<ApiServer>(
                config_manager_->GetApiConfig(),
                trading_engine_.get(),
                database_manager_.get());
            if (!api_server_->Initialize()) {
                LOG_ERROR("Failed to initialize API server");
                return false;
            }

            // Initialize WebSocket server
            websocket_server_ = std::make_unique<WebSocketServer>(
                config_manager_->GetWebSocketConfig(),
                trading_engine_.get());
            if (!websocket_server_->Initialize()) {
                LOG_ERROR("Failed to initialize WebSocket server");
                return false;
            }

            LOG_INFO("ZenFlow Backend initialized successfully");
            return true;

        } catch (const std::exception& e) {
            LOG_ERROR("Exception during initialization: " + std::string(e.what()));
            return false;
        }
    }

    bool Start() {
        try {
            // Start trading engine
            if (!trading_engine_->Start()) {
                LOG_ERROR("Failed to start trading engine");
                return false;
            }

            // Start database manager
            if (!database_manager_->Start()) {
                LOG_ERROR("Failed to start database manager");
                return false;
            }

            // Start WebSocket server
            if (!websocket_server_->Start()) {
                LOG_ERROR("Failed to start WebSocket server");
                return false;
            }

            // Start API server (this will block)
            if (!api_server_->Start()) {
                LOG_ERROR("Failed to start API server");
                return false;
            }

            LOG_INFO("ZenFlow Backend started successfully");
            LOG_INFO("API Server listening on port " + std::to_string(config_manager_->GetApiPort()));
            LOG_INFO("WebSocket Server listening on port " + std::to_string(config_manager_->GetWebSocketPort()));

            return true;

        } catch (const std::exception& e) {
            LOG_ERROR("Exception during startup: " + std::string(e.what()));
            return false;
        }
    }

    void Stop() {
        LOG_INFO("ZenFlow Backend shutting down...");

        if (api_server_) {
            api_server_->Stop();
        }

        if (websocket_server_) {
            websocket_server_->Stop();
        }

        if (trading_engine_) {
            trading_engine_->Stop();
        }

        if (database_manager_) {
            database_manager_->Stop();
        }

        LOG_INFO("ZenFlow Backend shutdown complete");
    }

    void Run() {
        if (!Initialize()) {
            std::cerr << "Failed to initialize ZenFlow Backend" << std::endl;
            return;
        }

        if (!Start()) {
            std::cerr << "Failed to start ZenFlow Backend" << std::endl;
            return;
        }

        // Keep the main thread alive
        while (running_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        Stop();
    }

    void Shutdown() {
        running_ = false;
    }

private:
    std::unique_ptr<ConfigManager> config_manager_;
    std::unique_ptr<DatabaseManager> database_manager_;
    std::unique_ptr<zenflow::TradingEngine> trading_engine_;
    std::unique_ptr<ApiServer> api_server_;
    std::unique_ptr<WebSocketServer> websocket_server_;
    
    std::atomic<bool> running_{true};

    void InitializeLogging() {
        auto logger = std::make_unique<zenflow::Logger>("ZenFlowBackend", zenflow::LogLevel::INFO);
        
        // Add console sink
        logger->AddSink(std::make_unique<zenflow::ConsoleSink>(true));
        
        // Add file sink
        logger->AddSink(std::make_unique<zenflow::FileSink>("logs/backend.log"));
        
        // Enable async logging for better performance
        logger->SetAsyncMode(true);
        
        zenflow::Logger::SetGlobalLogger(std::move(logger));
    }
};

// Global backend instance
std::unique_ptr<ZenFlowBackend> g_backend;

// Signal handler for graceful shutdown
void SignalHandler(int signal) {
    LOG_INFO("Received signal " + std::to_string(signal) + ", shutting down...");
    if (g_backend) {
        g_backend->Shutdown();
    }
}

void PrintUsage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [options]\n"
              << "Options:\n"
              << "  -h, --help     Show this help message\n"
              << "  -v, --version  Show version information\n"
              << "  -c, --config   Configuration file path (default: config/server.json)\n"
              << "  -d, --daemon   Run as daemon\n"
              << "  --log-level    Set log level (TRACE, DEBUG, INFO, WARNING, ERROR, CRITICAL)\n"
              << std::endl;
}

void PrintVersion() {
    std::cout << "ZenFlow Trading Backend v1.0.0\n"
              << "Built with C++17\n"
              << "Copyright (c) 2024 ZenFlow Trading\n"
              << std::endl;
}

int main(int argc, char* argv[]) {
    // Parse command line arguments
    bool daemon_mode = false;
    std::string config_file = "config/server.json";
    std::string log_level = "INFO";

    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            PrintUsage(argv[0]);
            return 0;
        } else if (arg == "-v" || arg == "--version") {
            PrintVersion();
            return 0;
        } else if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                config_file = argv[++i];
            } else {
                std::cerr << "Error: --config requires a file path" << std::endl;
                return 1;
            }
        } else if (arg == "-d" || arg == "--daemon") {
            daemon_mode = true;
        } else if (arg == "--log-level") {
            if (i + 1 < argc) {
                log_level = argv[++i];
            } else {
                std::cerr << "Error: --log-level requires a level" << std::endl;
                return 1;
            }
        } else {
            std::cerr << "Unknown option: " << arg << std::endl;
            PrintUsage(argv[0]);
            return 1;
        }
    }

    // Set up signal handlers
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
#ifndef _WIN32
    signal(SIGHUP, SignalHandler);
#endif

    try {
        // Create and run backend
        g_backend = std::make_unique<ZenFlowBackend>();
        g_backend->Run();
        
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred" << std::endl;
        return 1;
    }
}
