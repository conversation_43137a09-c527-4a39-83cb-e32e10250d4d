#pragma once

#include <string>
#include <memory>
#include <json/json.h>
#include "api_server.h"
#include "websocket_server.h"
#include "database_manager.h"
#include "../../core/engine/trading_engine.h"

class ConfigManager {
public:
    ConfigManager();
    explicit ConfigManager(const std::string& config_file);
    ~ConfigManager() = default;

    bool LoadConfig();
    bool LoadConfig(const std::string& config_file);
    bool SaveConfig();
    bool SaveConfig(const std::string& config_file);

    // Configuration getters
    ApiConfig GetApiConfig() const;
    WebSocketConfig GetWebSocketConfig() const;
    DatabaseConfig GetDatabaseConfig() const;
    zenflow::EngineConfig GetEngineConfig() const;

    // Configuration setters
    void SetApiConfig(const ApiConfig& config);
    void SetWebSocketConfig(const WebSocketConfig& config);
    void SetDatabaseConfig(const DatabaseConfig& config);
    void SetEngineConfig(const zenflow::EngineConfig& config);

    // Environment variable support
    void LoadFromEnvironment();
    std::string GetEnvironmentVariable(const std::string& name, const std::string& default_value = "") const;

    // Validation
    bool ValidateConfig() const;
    std::vector<std::string> GetValidationErrors() const;

    // Logging configuration
    struct LoggingConfig {
        std::string level = "INFO";
        std::string file_path = "logs/zenflow_backend.log";
        bool console_output = true;
        bool file_output = true;
        bool async_logging = true;
        size_t max_file_size_mb = 100;
        int max_files = 10;
    };
    
    LoggingConfig GetLoggingConfig() const;
    void SetLoggingConfig(const LoggingConfig& config);

    // Security configuration
    struct SecurityConfig {
        std::string jwt_secret = "change_this_in_production";
        int jwt_expiry_hours = 24;
        bool enable_rate_limiting = true;
        int api_rate_limit_per_minute = 100;
        int websocket_rate_limit_per_second = 10;
        bool enable_cors = true;
        std::vector<std::string> allowed_origins;
        bool enable_ssl = false;
        std::string ssl_cert_file;
        std::string ssl_key_file;
    };
    
    SecurityConfig GetSecurityConfig() const;
    void SetSecurityConfig(const SecurityConfig& config);

    // Performance configuration
    struct PerformanceConfig {
        int api_thread_count = 4;
        int websocket_max_connections = 1000;
        int database_max_connections = 10;
        int database_connection_timeout = 30;
        size_t message_queue_size = 10000;
        int ping_interval_seconds = 30;
        bool enable_compression = true;
    };
    
    PerformanceConfig GetPerformanceConfig() const;
    void SetPerformanceConfig(const PerformanceConfig& config);

private:
    std::string config_file_;
    Json::Value config_root_;
    
    // Configuration sections
    ApiConfig api_config_;
    WebSocketConfig websocket_config_;
    DatabaseConfig database_config_;
    zenflow::EngineConfig engine_config_;
    LoggingConfig logging_config_;
    SecurityConfig security_config_;
    PerformanceConfig performance_config_;
    
    // Helper methods
    void SetDefaultValues();
    void LoadApiConfig(const Json::Value& api_section);
    void LoadWebSocketConfig(const Json::Value& websocket_section);
    void LoadDatabaseConfig(const Json::Value& database_section);
    void LoadEngineConfig(const Json::Value& engine_section);
    void LoadLoggingConfig(const Json::Value& logging_section);
    void LoadSecurityConfig(const Json::Value& security_section);
    void LoadPerformanceConfig(const Json::Value& performance_section);
    
    Json::Value ApiConfigToJson() const;
    Json::Value WebSocketConfigToJson() const;
    Json::Value DatabaseConfigToJson() const;
    Json::Value EngineConfigToJson() const;
    Json::Value LoggingConfigToJson() const;
    Json::Value SecurityConfigToJson() const;
    Json::Value PerformanceConfigToJson() const;
    
    bool ValidateApiConfig() const;
    bool ValidateDatabaseConfig() const;
    bool ValidateSecurityConfig() const;
    
    template<typename T>
    T GetValue(const Json::Value& section, const std::string& key, const T& default_value) const;
    
    template<typename T>
    void SetValue(Json::Value& section, const std::string& key, const T& value);
};

// Configuration file template generator
class ConfigTemplate {
public:
    static std::string GenerateDefaultConfig();
    static std::string GenerateProductionConfig();
    static std::string GenerateDevelopmentConfig();
    static bool CreateConfigFile(const std::string& filename, const std::string& template_type = "default");
    
private:
    static Json::Value GetDefaultApiConfig();
    static Json::Value GetDefaultWebSocketConfig();
    static Json::Value GetDefaultDatabaseConfig();
    static Json::Value GetDefaultEngineConfig();
    static Json::Value GetDefaultLoggingConfig();
    static Json::Value GetDefaultSecurityConfig();
    static Json::Value GetDefaultPerformanceConfig();
};

// Environment-specific configuration loader
class EnvironmentConfig {
public:
    static ConfigManager LoadForEnvironment(const std::string& environment);
    static bool IsProduction();
    static bool IsDevelopment();
    static bool IsTesting();
    static std::string GetEnvironment();
    
private:
    static std::string GetConfigPath(const std::string& environment);
};

#endif // CONFIG_MANAGER_H
