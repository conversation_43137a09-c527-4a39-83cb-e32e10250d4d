#pragma once

#include <memory>
#include <unordered_map>
#include <vector>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <functional>
#include <chrono>

#include "../market_data/market_data_handler.h"
#include "../orders/order_manager.h"
#include "../risk/risk_manager.h"
#include "../strategies/strategy_manager.h"
#include "../utils/thread_pool.h"
#include "../utils/logger.h"

namespace zenflow {

struct EngineConfig {
    size_t max_threads = std::thread::hardware_concurrency();
    size_t max_orders_per_second = 1000;
    double max_position_size = 1000000.0;
    bool enable_risk_management = true;
    bool enable_logging = true;
    std::string log_level = "INFO";
};

class TradingEngine {
public:
    explicit TradingEngine(const EngineConfig& config = EngineConfig{});
    ~TradingEngine();

    // Core lifecycle methods
    bool Initialize();
    bool Start();
    bool Stop();
    bool IsRunning() const { return is_running_.load(); }

    // Market data management
    bool SubscribeToSymbol(const std::string& symbol);
    bool UnsubscribeFromSymbol(const std::string& symbol);
    void OnMarketDataUpdate(const MarketData& data);

    // Order management
    OrderId PlaceOrder(const OrderRequest& request);
    bool CancelOrder(OrderId order_id);
    bool ModifyOrder(OrderId order_id, const OrderModification& modification);
    std::vector<Order> GetActiveOrders() const;
    std::vector<Order> GetOrderHistory(const std::string& symbol = "") const;

    // Position management
    Position GetPosition(const std::string& symbol) const;
    std::vector<Position> GetAllPositions() const;
    double GetTotalPnL() const;
    double GetUnrealizedPnL() const;

    // Strategy management
    bool AddStrategy(std::unique_ptr<Strategy> strategy);
    bool RemoveStrategy(const std::string& strategy_id);
    bool StartStrategy(const std::string& strategy_id);
    bool StopStrategy(const std::string& strategy_id);
    std::vector<StrategyInfo> GetActiveStrategies() const;

    // Risk management
    bool SetRiskLimit(const std::string& symbol, const RiskLimit& limit);
    RiskStatus GetRiskStatus() const;
    bool EnableRiskManagement(bool enable);

    // Performance metrics
    PerformanceMetrics GetPerformanceMetrics() const;
    void ResetMetrics();

    // Event callbacks
    void SetOrderCallback(std::function<void(const OrderEvent&)> callback);
    void SetPositionCallback(std::function<void(const PositionEvent&)> callback);
    void SetRiskCallback(std::function<void(const RiskEvent&)> callback);

private:
    // Core components
    std::unique_ptr<MarketDataHandler> market_data_handler_;
    std::unique_ptr<OrderManager> order_manager_;
    std::unique_ptr<RiskManager> risk_manager_;
    std::unique_ptr<StrategyManager> strategy_manager_;
    std::unique_ptr<ThreadPool> thread_pool_;
    std::unique_ptr<Logger> logger_;

    // Configuration
    EngineConfig config_;

    // State management
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_initialized_{false};
    mutable std::mutex state_mutex_;

    // Event processing
    std::queue<std::function<void()>> event_queue_;
    std::mutex event_queue_mutex_;
    std::condition_variable event_condition_;
    std::thread event_processor_thread_;

    // Performance tracking
    mutable std::mutex metrics_mutex_;
    PerformanceMetrics performance_metrics_;
    std::chrono::high_resolution_clock::time_point start_time_;

    // Callbacks
    std::function<void(const OrderEvent&)> order_callback_;
    std::function<void(const PositionEvent&)> position_callback_;
    std::function<void(const RiskEvent&)> risk_callback_;

    // Internal methods
    void ProcessEvents();
    void UpdatePerformanceMetrics();
    void HandleOrderEvent(const OrderEvent& event);
    void HandlePositionEvent(const PositionEvent& event);
    void HandleRiskEvent(const RiskEvent& event);
    void LogEvent(const std::string& message, LogLevel level = LogLevel::INFO);

    // Thread safety helpers
    template<typename T>
    T SafeRead(const T& value, std::mutex& mutex) const {
        std::lock_guard<std::mutex> lock(mutex);
        return value;
    }

    template<typename T>
    void SafeWrite(T& target, const T& value, std::mutex& mutex) {
        std::lock_guard<std::mutex> lock(mutex);
        target = value;
    }
};

// Factory function for creating trading engine instances
std::unique_ptr<TradingEngine> CreateTradingEngine(const EngineConfig& config = EngineConfig{});

} // namespace zenflow
