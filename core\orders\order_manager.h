#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <atomic>
#include <mutex>
#include <functional>
#include <chrono>
#include <queue>

namespace zenflow {

using OrderId = uint64_t;
constexpr OrderId INVALID_ORDER_ID = 0;

enum class OrderType {
    MARKET,
    LIMIT,
    STOP,
    STOP_LIMIT,
    TRAILING_STOP
};

enum class OrderSide {
    BUY,
    SELL
};

enum class OrderStatus {
    PENDING,
    SUBMITTED,
    PARTIALLY_FILLED,
    FILLED,
    CANCELLED,
    REJECTED,
    EXPIRED
};

enum class TimeInForce {
    DAY,
    GTC,  // Good Till Cancelled
    IOC,  // Immediate Or Cancel
    FOK   // Fill Or Kill
};

struct OrderRequest {
    std::string symbol;
    OrderType type;
    OrderSide side;
    double quantity;
    double price;           // For limit orders
    double stop_price;      // For stop orders
    TimeInForce time_in_force = TimeInForce::DAY;
    std::string strategy_id;
    std::unordered_map<std::string, std::string> metadata;
    
    bool IsValid() const {
        return !symbol.empty() && quantity > 0 && 
               (type == OrderType::MARKET || price > 0);
    }
};

struct OrderModification {
    double new_quantity = 0;
    double new_price = 0;
    double new_stop_price = 0;
    bool modify_quantity = false;
    bool modify_price = false;
    bool modify_stop_price = false;
};

struct Order {
    OrderId id;
    std::string symbol;
    OrderType type;
    OrderSide side;
    double quantity;
    double filled_quantity = 0;
    double remaining_quantity;
    double price;
    double stop_price = 0;
    double average_fill_price = 0;
    OrderStatus status;
    TimeInForce time_in_force;
    std::string strategy_id;
    int64_t created_time;
    int64_t updated_time;
    std::string exchange;
    std::unordered_map<std::string, std::string> metadata;
    
    double GetRemainingQuantity() const {
        return quantity - filled_quantity;
    }
    
    bool IsActive() const {
        return status == OrderStatus::PENDING || 
               status == OrderStatus::SUBMITTED || 
               status == OrderStatus::PARTIALLY_FILLED;
    }
    
    std::string ToString() const {
        return std::to_string(id) + " " + symbol + " " + 
               (side == OrderSide::BUY ? "BUY" : "SELL") + " " +
               std::to_string(quantity) + "@" + std::to_string(price);
    }
};

struct Fill {
    OrderId order_id;
    std::string symbol;
    OrderSide side;
    double quantity;
    double price;
    int64_t timestamp;
    std::string exchange;
    std::string execution_id;
    double commission = 0;
    
    std::string ToString() const {
        return std::to_string(order_id) + " " + symbol + " " +
               std::to_string(quantity) + "@" + std::to_string(price);
    }
};

struct Position {
    std::string symbol;
    double quantity = 0;
    double average_price = 0;
    double unrealized_pnl = 0;
    double realized_pnl = 0;
    int64_t updated_time;
    
    bool IsLong() const { return quantity > 0; }
    bool IsShort() const { return quantity < 0; }
    bool IsFlat() const { return quantity == 0; }
    
    std::string ToString() const {
        return symbol + " " + std::to_string(quantity) + "@" + std::to_string(average_price);
    }
};

enum class OrderEventType {
    ORDER_SUBMITTED,
    ORDER_FILLED,
    ORDER_PARTIALLY_FILLED,
    ORDER_CANCELLED,
    ORDER_REJECTED,
    ORDER_MODIFIED
};

struct OrderEvent {
    OrderEventType type;
    OrderId order_id;
    std::string symbol;
    std::string message;
    int64_t timestamp;
    
    std::string ToString() const {
        return std::to_string(order_id) + " " + message;
    }
};

struct PositionEvent {
    std::string symbol;
    double old_quantity;
    double new_quantity;
    double price;
    int64_t timestamp;

    std::string ToString() const {
        return symbol + " " + std::to_string(old_quantity) + " -> " + std::to_string(new_quantity);
    }
};

struct Fill {
    OrderId order_id;
    std::string symbol;
    OrderSide side;
    double quantity;
    double price;
    double commission = 0.0;
    std::string exchange;
    std::string execution_id;
    int64_t timestamp;
    std::unordered_map<std::string, std::string> metadata;

    double GetValue() const {
        return quantity * price;
    }

    std::string ToString() const {
        return std::to_string(order_id) + " " + symbol + " " +
               std::to_string(quantity) + "@" + std::to_string(price);
    }
};

class OrderManager {
public:
    OrderManager();
    ~OrderManager();

    // Lifecycle management
    bool Initialize();
    bool Start();
    bool Stop();
    bool IsRunning() const { return is_running_.load(); }

    // Order management
    OrderId PlaceOrder(const OrderRequest& request);
    bool CancelOrder(OrderId order_id);
    bool ModifyOrder(OrderId order_id, const OrderModification& modification);
    
    // Order queries
    Order GetOrder(OrderId order_id) const;
    std::vector<Order> GetActiveOrders() const;
    std::vector<Order> GetOrderHistory(const std::string& symbol = "") const;
    std::vector<Order> GetOrdersByStrategy(const std::string& strategy_id) const;
    
    // Fill management
    bool ProcessFill(const Fill& fill);
    std::vector<Fill> GetFills(OrderId order_id = INVALID_ORDER_ID) const;
    std::vector<Fill> GetFillsBySymbol(const std::string& symbol) const;
    
    // Position management
    Position GetPosition(const std::string& symbol) const;
    std::vector<Position> GetAllPositions() const;
    double GetTotalPnL() const;
    double GetUnrealizedPnL(const std::string& symbol, double current_price) const;
    
    // Statistics
    size_t GetTotalOrderCount() const { return next_order_id_.load() - 1; }
    size_t GetActiveOrderCount() const;
    size_t GetFilledOrderCount() const;
    double GetTotalVolume() const;
    
    // Callbacks
    void SetEventCallback(std::function<void(const OrderEvent&)> callback);
    void SetFillCallback(std::function<void(const Fill&)> callback);
    void SetPositionCallback(std::function<void(const PositionEvent&)> callback);

private:
    // Order storage
    std::unordered_map<OrderId, Order> orders_;
    std::unordered_map<std::string, std::vector<OrderId>> symbol_orders_;
    std::unordered_map<std::string, std::vector<OrderId>> strategy_orders_;
    mutable std::mutex orders_mutex_;
    
    // Fill storage
    std::vector<Fill> fills_;
    std::unordered_map<OrderId, std::vector<Fill>> order_fills_;
    mutable std::mutex fills_mutex_;
    
    // Position storage
    std::unordered_map<std::string, Position> positions_;
    mutable std::mutex positions_mutex_;
    
    // State management
    std::atomic<OrderId> next_order_id_{1};
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_initialized_{false};
    
    // Callbacks
    std::function<void(const OrderEvent&)> event_callback_;
    std::function<void(const Fill&)> fill_callback_;
    std::function<void(const PositionEvent&)> position_callback_;
    
    // Internal methods
    OrderId GenerateOrderId();
    bool ValidateOrderRequest(const OrderRequest& request) const;
    void UpdateOrderStatus(OrderId order_id, OrderStatus new_status);
    void UpdatePosition(const Fill& fill);
    void EmitOrderEvent(OrderEventType type, OrderId order_id, const std::string& message);
    void EmitPositionEvent(const std::string& symbol, double old_qty, double new_qty, double price);
    
    // Helper methods
    int64_t GetCurrentTimestamp() const;
    double CalculateAveragePrice(double current_avg, double current_qty, 
                                double fill_price, double fill_qty) const;
};

} // namespace zenflow
