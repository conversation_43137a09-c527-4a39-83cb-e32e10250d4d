#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QLineEdit>
#include <QRadioButton>
#include <QButtonGroup>
#include <QTableWidget>
#include <QHeaderView>
#include <QTimer>
#include <memory>

#include "../../../core/engine/trading_engine.h"

class TradingWidget : public QWidget {
    Q_OBJECT

public:
    explicit TradingWidget(QWidget* parent = nullptr);
    ~TradingWidget() = default;

    void SetTradingEngine(zenflow::TradingEngine* engine);
    void QuickBuy(const QString& symbol);
    void QuickSell(const QString& symbol);

signals:
    void OrderPlaced(const QString& symbol, double quantity, double price);
    void OrderCancelled(int orderId);

private slots:
    void OnSymbolChanged();
    void OnOrderTypeChanged();
    void OnBuyClicked();
    void OnSellClicked();
    void OnCancelOrderClicked();
    void OnCancelAllClicked();
    void OnQuantityChanged();
    void OnPriceChanged();
    void UpdateOrderBook();
    void UpdatePositions();
    void UpdateActiveOrders();

private:
    void SetupUI();
    void SetupOrderEntry();
    void SetupOrderBook();
    void SetupPositions();
    void SetupActiveOrders();
    void UpdateMarketData();
    void ValidateOrder();
    void PlaceOrder(zenflow::OrderSide side);
    void PopulateOrderTable();
    void PopulatePositionTable();

    // UI Components
    QVBoxLayout* main_layout_;
    
    // Order entry group
    QGroupBox* order_entry_group_;
    QGridLayout* order_entry_layout_;
    QComboBox* symbol_combo_;
    QButtonGroup* order_type_group_;
    QRadioButton* market_radio_;
    QRadioButton* limit_radio_;
    QRadioButton* stop_radio_;
    QRadioButton* stop_limit_radio_;
    QDoubleSpinBox* quantity_spin_;
    QDoubleSpinBox* price_spin_;
    QDoubleSpinBox* stop_price_spin_;
    QComboBox* time_in_force_combo_;
    QPushButton* buy_button_;
    QPushButton* sell_button_;
    QLabel* last_price_label_;
    QLabel* bid_ask_label_;
    QLabel* position_label_;
    
    // Order book group
    QGroupBox* order_book_group_;
    QVBoxLayout* order_book_layout_;
    QTableWidget* order_book_table_;
    
    // Positions group
    QGroupBox* positions_group_;
    QVBoxLayout* positions_layout_;
    QTableWidget* positions_table_;
    
    // Active orders group
    QGroupBox* active_orders_group_;
    QVBoxLayout* active_orders_layout_;
    QTableWidget* active_orders_table_;
    QPushButton* cancel_all_button_;
    
    // Data and state
    zenflow::TradingEngine* trading_engine_;
    QString current_symbol_;
    double last_price_;
    double bid_price_;
    double ask_price_;
    double current_position_;
    
    // Timers
    QTimer* market_data_timer_;
    QTimer* order_update_timer_;
    
    // Constants
    static constexpr int MARKET_DATA_UPDATE_INTERVAL = 100; // 100ms
    static constexpr int ORDER_UPDATE_INTERVAL = 500;      // 500ms
};

// Rate limiting helper class
class RateLimiter {
public:
    explicit RateLimiter(int max_requests_per_second = 10, int burst_capacity = 20);
    
    bool AllowRequest();
    void Reset();
    
    int GetRequestsPerSecond() const { return max_requests_per_second_; }
    int GetBurstCapacity() const { return burst_capacity_; }
    int GetCurrentCount() const { return current_count_; }
    
private:
    int max_requests_per_second_;
    int burst_capacity_;
    int current_count_;
    std::chrono::steady_clock::time_point last_reset_;
    std::chrono::steady_clock::time_point last_request_;
    
    void UpdateCounts();
};

// Order validation helper
class OrderValidator {
public:
    struct ValidationResult {
        bool is_valid = false;
        QString error_message;
        QString warning_message;
    };
    
    static ValidationResult ValidateOrder(
        const QString& symbol,
        zenflow::OrderType type,
        zenflow::OrderSide side,
        double quantity,
        double price,
        double stop_price = 0.0
    );
    
private:
    static bool IsValidSymbol(const QString& symbol);
    static bool IsValidQuantity(double quantity);
    static bool IsValidPrice(double price);
    static bool IsMarketOpen();
};

// Quick trade dialog
class QuickTradeDialog : public QDialog {
    Q_OBJECT
    
public:
    explicit QuickTradeDialog(const QString& symbol, zenflow::OrderSide side, QWidget* parent = nullptr);
    
    QString GetSymbol() const;
    double GetQuantity() const;
    double GetPrice() const;
    zenflow::OrderType GetOrderType() const;
    
private slots:
    void OnOrderTypeChanged();
    void OnAccept();
    
private:
    void SetupUI();
    void UpdatePriceFields();
    
    QString symbol_;
    zenflow::OrderSide side_;
    
    QComboBox* order_type_combo_;
    QDoubleSpinBox* quantity_spin_;
    QDoubleSpinBox* price_spin_;
    QLabel* market_price_label_;
    QPushButton* ok_button_;
    QPushButton* cancel_button_;
};

#endif // TRADING_WIDGET_H
