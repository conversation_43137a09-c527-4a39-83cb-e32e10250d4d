#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QTableWidget>
#include <QLabel>
#include <QTimer>
#include "../../../core/market_data/market_data_handler.h"

class OrderBookWidget : public QWidget {
    Q_OBJECT

public:
    explicit OrderBookWidget(QWidget* parent = nullptr);
    void SetSymbol(const QString& symbol);
    void UpdateOrderBook();

private:
    void SetupUI();
    void PopulateOrderBook();
    
    QVBoxLayout* main_layout_;
    QLabel* symbol_label_;
    QTableWidget* orderbook_table_;
    QTimer* update_timer_;
    
    QString current_symbol_;
    zenflow::OrderBook order_book_data_;
};

#endif
