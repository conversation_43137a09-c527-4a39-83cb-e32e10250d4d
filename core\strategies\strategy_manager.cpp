#include "strategy_manager.h"
#include "../engine/trading_engine.h"
#include "../utils/logger.h"
#include <algorithm>
#include <stdexcept>

namespace zenflow {

// Strategy base class implementation
Strategy::Strategy(const std::string& id, const std::string& name)
    : id_(id), name_(name) {
    start_time_ = std::chrono::high_resolution_clock::now();
    last_update_time_ = start_time_;
    
    // Initialize performance metrics
    performance_.strategy_id = id;
    performance_.total_pnl = 0.0;
    performance_.unrealized_pnl = 0.0;
    performance_.realized_pnl = 0.0;
    performance_.total_trades = 0;
    performance_.winning_trades = 0;
    performance_.losing_trades = 0;
    performance_.win_rate = 0.0;
    performance_.sharpe_ratio = 0.0;
    performance_.max_drawdown = 0.0;
    performance_.volatility = 0.0;
    performance_.last_updated = std::chrono::duration_cast<std::chrono::milliseconds>(
        start_time_.time_since_epoch()).count();
}

void Strategy::SetParameter(const std::string& name, double value) {
    std::lock_guard<std::mutex> lock(parameters_mutex_);
    parameters_[name] = value;
}

double Strategy::GetParameter(const std::string& name) const {
    std::lock_guard<std::mutex> lock(parameters_mutex_);
    auto it = parameters_.find(name);
    return (it != parameters_.end()) ? it->second : 0.0;
}

std::unordered_map<std::string, double> Strategy::GetAllParameters() const {
    std::lock_guard<std::mutex> lock(parameters_mutex_);
    return parameters_;
}

StrategyPerformance Strategy::GetPerformance() const {
    std::lock_guard<std::mutex> lock(performance_mutex_);
    return performance_;
}

void Strategy::UpdatePerformance() {
    std::lock_guard<std::mutex> lock(performance_mutex_);
    
    auto now = std::chrono::high_resolution_clock::now();
    performance_.last_updated = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()).count();
    
    // Calculate win rate
    if (performance_.total_trades > 0) {
        performance_.win_rate = static_cast<double>(performance_.winning_trades) / 
                               performance_.total_trades;
    }
    
    last_update_time_ = now;
}

OrderId Strategy::PlaceOrder(const OrderRequest& request) {
    if (trading_engine_) {
        return trading_engine_->PlaceOrder(request);
    }
    return 0;
}

bool Strategy::CancelOrder(OrderId order_id) {
    if (trading_engine_) {
        return trading_engine_->CancelOrder(order_id);
    }
    return false;
}

Position Strategy::GetPosition(const std::string& symbol) const {
    if (trading_engine_) {
        return trading_engine_->GetPosition(symbol);
    }
    return Position{};
}

void Strategy::LogMessage(const std::string& message, LogLevel level) {
    std::string formatted_message = "[" + name_ + "] " + message;
    if (auto logger = Logger::GetGlobalLogger()) {
        logger->Log(level, formatted_message);
    }
}

// StrategyManager implementation
StrategyManager::StrategyManager() {
    last_performance_update_ = std::chrono::high_resolution_clock::now();
}

StrategyManager::~StrategyManager() {
    if (is_running_.load()) {
        Stop();
    }
}

bool StrategyManager::Initialize() {
    if (is_initialized_.load()) {
        return true;
    }

    try {
        // Initialize any required resources
        is_initialized_.store(true);
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to initialize StrategyManager: " + std::string(e.what()));
        return false;
    }
}

bool StrategyManager::Start() {
    if (!is_initialized_.load()) {
        LOG_ERROR("StrategyManager not initialized");
        return false;
    }

    if (is_running_.load()) {
        return true;
    }

    try {
        is_running_.store(true);
        LOG_INFO("StrategyManager started successfully");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to start StrategyManager: " + std::string(e.what()));
        return false;
    }
}

bool StrategyManager::Stop() {
    if (!is_running_.load()) {
        return true;
    }

    try {
        // Stop all running strategies
        std::lock_guard<std::mutex> lock(strategies_mutex_);
        for (auto& [id, strategy] : strategies_) {
            if (strategy->GetState() == StrategyState::RUNNING) {
                strategy->Stop();
            }
        }

        is_running_.store(false);
        LOG_INFO("StrategyManager stopped successfully");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to stop StrategyManager: " + std::string(e.what()));
        return false;
    }
}

bool StrategyManager::AddStrategy(std::unique_ptr<Strategy> strategy) {
    if (!strategy) {
        LOG_ERROR("Cannot add null strategy");
        return false;
    }

    if (!ValidateStrategy(strategy.get())) {
        LOG_ERROR("Strategy validation failed: " + strategy->GetId());
        return false;
    }

    std::lock_guard<std::mutex> lock(strategies_mutex_);
    
    if (strategies_.size() >= max_strategies_) {
        LOG_ERROR("Maximum number of strategies reached: " + std::to_string(max_strategies_));
        return false;
    }

    if (strategies_.find(strategy->GetId()) != strategies_.end()) {
        LOG_ERROR("Strategy with ID already exists: " + strategy->GetId());
        return false;
    }

    // Set trading engine reference
    strategy->SetTradingEngine(trading_engine_);
    
    // Initialize the strategy
    if (!strategy->Initialize()) {
        LOG_ERROR("Failed to initialize strategy: " + strategy->GetId());
        return false;
    }

    std::string strategy_id = strategy->GetId();
    strategies_[strategy_id] = std::move(strategy);
    
    LOG_INFO("Strategy added successfully: " + strategy_id);
    NotifyStrategyStateChange(strategy_id, StrategyState::STOPPED);
    
    return true;
}

bool StrategyManager::RemoveStrategy(const std::string& strategy_id) {
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    
    auto it = strategies_.find(strategy_id);
    if (it == strategies_.end()) {
        LOG_WARNING("Strategy not found: " + strategy_id);
        return false;
    }

    // Stop the strategy if it's running
    if (it->second->GetState() == StrategyState::RUNNING) {
        it->second->Stop();
    }

    strategies_.erase(it);
    LOG_INFO("Strategy removed: " + strategy_id);
    
    return true;
}

bool StrategyManager::StartStrategy(const std::string& strategy_id) {
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    
    auto it = strategies_.find(strategy_id);
    if (it == strategies_.end()) {
        LOG_ERROR("Strategy not found: " + strategy_id);
        return false;
    }

    if (it->second->GetState() == StrategyState::RUNNING) {
        LOG_WARNING("Strategy already running: " + strategy_id);
        return true;
    }

    NotifyStrategyStateChange(strategy_id, StrategyState::STARTING);
    
    if (it->second->Start()) {
        NotifyStrategyStateChange(strategy_id, StrategyState::RUNNING);
        LOG_INFO("Strategy started: " + strategy_id);
        return true;
    } else {
        NotifyStrategyStateChange(strategy_id, StrategyState::ERROR);
        LOG_ERROR("Failed to start strategy: " + strategy_id);
        return false;
    }
}

bool StrategyManager::StopStrategy(const std::string& strategy_id) {
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    
    auto it = strategies_.find(strategy_id);
    if (it == strategies_.end()) {
        LOG_ERROR("Strategy not found: " + strategy_id);
        return false;
    }

    if (it->second->GetState() == StrategyState::STOPPED) {
        LOG_WARNING("Strategy already stopped: " + strategy_id);
        return true;
    }

    NotifyStrategyStateChange(strategy_id, StrategyState::STOPPING);
    
    if (it->second->Stop()) {
        NotifyStrategyStateChange(strategy_id, StrategyState::STOPPED);
        LOG_INFO("Strategy stopped: " + strategy_id);
        return true;
    } else {
        NotifyStrategyStateChange(strategy_id, StrategyState::ERROR);
        LOG_ERROR("Failed to stop strategy: " + strategy_id);
        return false;
    }
}

bool StrategyManager::RestartStrategy(const std::string& strategy_id) {
    return StopStrategy(strategy_id) && StartStrategy(strategy_id);
}

std::vector<StrategyInfo> StrategyManager::GetActiveStrategies() const {
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    std::vector<StrategyInfo> active_strategies;
    
    for (const auto& [id, strategy] : strategies_) {
        if (strategy->GetState() == StrategyState::RUNNING) {
            StrategyInfo info;
            info.id = strategy->GetId();
            info.name = strategy->GetName();
            info.state = strategy->GetState();
            info.parameters = strategy->GetAllParameters();
            
            auto performance = strategy->GetPerformance();
            info.pnl = performance.total_pnl;
            info.last_update = performance.last_updated;
            
            active_strategies.push_back(info);
        }
    }
    
    return active_strategies;
}

std::vector<StrategyInfo> StrategyManager::GetAllStrategies() const {
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    std::vector<StrategyInfo> all_strategies;
    
    for (const auto& [id, strategy] : strategies_) {
        StrategyInfo info;
        info.id = strategy->GetId();
        info.name = strategy->GetName();
        info.state = strategy->GetState();
        info.parameters = strategy->GetAllParameters();
        
        auto performance = strategy->GetPerformance();
        info.pnl = performance.total_pnl;
        info.last_update = performance.last_updated;
        
        all_strategies.push_back(info);
    }
    
    return all_strategies;
}

void StrategyManager::OnMarketData(const MarketData& data) {
    if (!is_running_.load()) {
        return;
    }

    std::lock_guard<std::mutex> lock(strategies_mutex_);
    for (auto& [id, strategy] : strategies_) {
        if (strategy->GetState() == StrategyState::RUNNING) {
            try {
                strategy->OnMarketData(data);
            } catch (const std::exception& e) {
                LOG_ERROR("Strategy " + id + " market data error: " + e.what());
            }
        }
    }
}

void StrategyManager::OnOrderEvent(const OrderEvent& event) {
    if (!is_running_.load()) {
        return;
    }

    std::lock_guard<std::mutex> lock(strategies_mutex_);
    for (auto& [id, strategy] : strategies_) {
        if (strategy->GetState() == StrategyState::RUNNING) {
            try {
                strategy->OnOrderEvent(event);
            } catch (const std::exception& e) {
                LOG_ERROR("Strategy " + id + " order event error: " + e.what());
            }
        }
    }
}

void StrategyManager::SetTradingEngine(TradingEngine* engine) {
    trading_engine_ = engine;
    
    // Update all existing strategies
    std::lock_guard<std::mutex> lock(strategies_mutex_);
    for (auto& [id, strategy] : strategies_) {
        strategy->SetTradingEngine(engine);
    }
}

void StrategyManager::SetStrategyEventCallback(std::function<void(const std::string&, StrategyState)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    strategy_event_callback_ = callback;
}

void StrategyManager::NotifyStrategyStateChange(const std::string& strategy_id, StrategyState state) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (strategy_event_callback_) {
        strategy_event_callback_(strategy_id, state);
    }
}

bool StrategyManager::ValidateStrategy(const Strategy* strategy) const {
    if (!strategy) {
        return false;
    }
    
    if (strategy->GetId().empty() || strategy->GetName().empty()) {
        return false;
    }
    
    return true;
}

} // namespace zenflow
