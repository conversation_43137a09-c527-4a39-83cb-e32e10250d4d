#include "logger.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <filesystem>

namespace zenflow {

// Static members
std::unique_ptr<Logger> Logger::global_logger_ = nullptr;
std::mutex Logger::global_logger_mutex_;

// LogEntry implementation
std::string LogEntry::ToString() const {
    std::ostringstream oss;
    
    // Format timestamp
    auto time_t = std::chrono::system_clock::to_time_t(timestamp);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        timestamp.time_since_epoch()) % 1000;
    
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    // Add level
    oss << " [" << LogLevelToString(level) << "]";
    
    // Add thread ID if available
    if (thread_id != std::thread::id{}) {
        oss << " [" << thread_id << "]";
    }
    
    // Add logger name
    if (!logger_name.empty()) {
        oss << " [" << logger_name << "]";
    }
    
    // Add file and line if available
    if (!file.empty() && line > 0) {
        std::filesystem::path path(file);
        oss << " [" << path.filename().string() << ":" << line;
        if (!function.empty()) {
            oss << " " << function << "()";
        }
        oss << "]";
    }
    
    oss << " " << message;
    
    return oss.str();
}

std::string LogLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE: return "TRACE";
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARN";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::CRITICAL: return "CRIT";
        default: return "UNKNOWN";
    }
}

LogLevel LogLevelFromString(const std::string& level_str) {
    if (level_str == "TRACE") return LogLevel::TRACE;
    if (level_str == "DEBUG") return LogLevel::DEBUG;
    if (level_str == "INFO") return LogLevel::INFO;
    if (level_str == "WARNING" || level_str == "WARN") return LogLevel::WARNING;
    if (level_str == "ERROR") return LogLevel::ERROR;
    if (level_str == "CRITICAL" || level_str == "CRIT") return LogLevel::CRITICAL;
    return LogLevel::INFO; // Default
}

// ConsoleSink implementation
ConsoleSink::ConsoleSink(bool use_colors) : use_colors_(use_colors) {}

void ConsoleSink::Write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(console_mutex_);
    
    if (use_colors_) {
        std::cout << GetColorCode(entry.level);
    }
    
    std::cout << entry.ToString();
    
    if (use_colors_) {
        std::cout << GetResetCode();
    }
    
    std::cout << std::endl;
}

void ConsoleSink::Flush() {
    std::cout.flush();
}

std::string ConsoleSink::GetColorCode(LogLevel level) const {
    switch (level) {
        case LogLevel::TRACE: return "\033[37m";    // White
        case LogLevel::DEBUG: return "\033[36m";    // Cyan
        case LogLevel::INFO: return "\033[32m";     // Green
        case LogLevel::WARNING: return "\033[33m";  // Yellow
        case LogLevel::ERROR: return "\033[31m";    // Red
        case LogLevel::CRITICAL: return "\033[35m"; // Magenta
        default: return "";
    }
}

std::string ConsoleSink::GetResetCode() const {
    return "\033[0m";
}

// FileSink implementation
FileSink::FileSink(const std::string& filename, bool append) 
    : filename_(filename), max_file_size_(100 * 1024 * 1024) { // 100MB default
    
    // Create directory if it doesn't exist
    std::filesystem::path path(filename);
    if (path.has_parent_path()) {
        std::filesystem::create_directories(path.parent_path());
    }
    
    auto mode = append ? std::ios::app : std::ios::trunc;
    file_.open(filename_, std::ios::out | mode);
    
    if (!file_.is_open()) {
        throw std::runtime_error("Failed to open log file: " + filename_);
    }
}

FileSink::~FileSink() {
    if (file_.is_open()) {
        file_.close();
    }
}

void FileSink::Write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (!file_.is_open()) {
        return;
    }
    
    file_ << entry.ToString() << std::endl;
    
    // Check if rotation is needed
    if (GetFileSize() > max_file_size_) {
        RotateFile();
    }
}

void FileSink::Flush() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    if (file_.is_open()) {
        file_.flush();
    }
}

void FileSink::Rotate(size_t max_size_mb) {
    max_file_size_ = max_size_mb * 1024 * 1024;
}

void FileSink::RotateFile() {
    if (!file_.is_open()) {
        return;
    }
    
    file_.close();
    
    // Rename current file with timestamp
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << filename_ << "." << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
    
    std::filesystem::rename(filename_, oss.str());
    
    // Open new file
    file_.open(filename_, std::ios::out | std::ios::trunc);
}

size_t FileSink::GetFileSize() const {
    if (!file_.is_open()) {
        return 0;
    }
    
    auto current_pos = file_.tellp();
    return static_cast<size_t>(current_pos);
}

// Logger implementation
Logger::Logger(const std::string& name, LogLevel min_level) 
    : name_(name), min_level_(min_level) {}

Logger::~Logger() {
    if (async_mode_.load()) {
        shutdown_.store(true);
        queue_condition_.notify_all();
        if (async_thread_.joinable()) {
            async_thread_.join();
        }
    }
}

void Logger::Log(LogLevel level, const std::string& message, 
                const std::string& file, int line, const std::string& function) {
    if (!ShouldLog(level)) {
        return;
    }
    
    LogEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.level = level;
    entry.message = message;
    entry.logger_name = name_;
    entry.thread_id = std::this_thread::get_id();
    entry.file = file;
    entry.line = line;
    entry.function = function;
    
    if (async_mode_.load()) {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        log_queue_.push(entry);
        queue_condition_.notify_one();
    } else {
        WriteToSinks(entry);
    }
}

void Logger::Trace(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::TRACE, message, file, line, function);
}

void Logger::Debug(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::DEBUG, message, file, line, function);
}

void Logger::Info(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::INFO, message, file, line, function);
}

void Logger::Warning(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::WARNING, message, file, line, function);
}

void Logger::Error(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::ERROR, message, file, line, function);
}

void Logger::Critical(const std::string& message, const std::string& file, int line, const std::string& function) {
    Log(LogLevel::CRITICAL, message, file, line, function);
}

void Logger::AddSink(std::unique_ptr<LogSink> sink) {
    std::lock_guard<std::mutex> lock(sinks_mutex_);
    sinks_.push_back(std::move(sink));
}

void Logger::RemoveAllSinks() {
    std::lock_guard<std::mutex> lock(sinks_mutex_);
    sinks_.clear();
}

void Logger::SetLevel(LogLevel level) {
    min_level_.store(level);
}

LogLevel Logger::GetLevel() const {
    return min_level_.load();
}

void Logger::SetAsyncMode(bool enable) {
    if (enable && !async_mode_.load()) {
        async_mode_.store(true);
        async_thread_ = std::thread(&Logger::ProcessAsyncLogs, this);
    } else if (!enable && async_mode_.load()) {
        shutdown_.store(true);
        queue_condition_.notify_all();
        if (async_thread_.joinable()) {
            async_thread_.join();
        }
        async_mode_.store(false);
        shutdown_.store(false);
    }
}

void Logger::Flush() {
    if (async_mode_.load()) {
        // Wait for queue to be empty
        std::unique_lock<std::mutex> lock(queue_mutex_);
        queue_condition_.wait(lock, [this] { return log_queue_.empty(); });
    }
    
    std::lock_guard<std::mutex> lock(sinks_mutex_);
    for (auto& sink : sinks_) {
        sink->Flush();
    }
}

void Logger::ProcessAsyncLogs() {
    while (!shutdown_.load()) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        queue_condition_.wait(lock, [this] { return !log_queue_.empty() || shutdown_.load(); });
        
        while (!log_queue_.empty()) {
            LogEntry entry = log_queue_.front();
            log_queue_.pop();
            lock.unlock();
            
            WriteToSinks(entry);
            
            lock.lock();
        }
    }
    
    // Process remaining logs
    std::lock_guard<std::mutex> lock(queue_mutex_);
    while (!log_queue_.empty()) {
        WriteToSinks(log_queue_.front());
        log_queue_.pop();
    }
}

void Logger::WriteToSinks(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(sinks_mutex_);
    for (auto& sink : sinks_) {
        try {
            sink->Write(entry);
        } catch (const std::exception& e) {
            // Avoid infinite recursion by writing to stderr directly
            std::cerr << "Logger sink error: " << e.what() << std::endl;
        }
    }
}

bool Logger::ShouldLog(LogLevel level) const {
    return level >= min_level_.load();
}

// Static methods
Logger& Logger::GetGlobalLogger() {
    std::lock_guard<std::mutex> lock(global_logger_mutex_);
    if (!global_logger_) {
        global_logger_ = std::make_unique<Logger>("Global", LogLevel::INFO);
        global_logger_->AddSink(std::make_unique<ConsoleSink>(true));
    }
    return *global_logger_;
}

void Logger::SetGlobalLogger(std::unique_ptr<Logger> logger) {
    std::lock_guard<std::mutex> lock(global_logger_mutex_);
    global_logger_ = std::move(logger);
}

} // namespace zenflow
