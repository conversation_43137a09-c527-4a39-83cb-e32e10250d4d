#include "trading_engine.h"
#include <algorithm>
#include <stdexcept>

namespace zenflow {

TradingEngine::TradingEngine(const EngineConfig& config) 
    : config_(config), start_time_(std::chrono::high_resolution_clock::now()) {
    
    // Initialize core components
    market_data_handler_ = std::make_unique<MarketDataHandler>();
    order_manager_ = std::make_unique<OrderManager>();
    risk_manager_ = std::make_unique<RiskManager>();
    strategy_manager_ = std::make_unique<StrategyManager>();
    thread_pool_ = std::make_unique<ThreadPool>(config_.max_threads);
    
    if (config_.enable_logging) {
        logger_ = std::make_unique<Logger>(config_.log_level);
    }
}

TradingEngine::~TradingEngine() {
    if (is_running_.load()) {
        Stop();
    }
}

bool TradingEngine::Initialize() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    if (is_initialized_.load()) {
        LogEvent("Trading engine already initialized", LogLevel::WARNING);
        return true;
    }

    try {
        // Initialize market data handler
        if (!market_data_handler_->Initialize()) {
            LogEvent("Failed to initialize market data handler", LogLevel::ERROR);
            return false;
        }

        // Initialize order manager
        if (!order_manager_->Initialize()) {
            LogEvent("Failed to initialize order manager", LogLevel::ERROR);
            return false;
        }

        // Initialize risk manager
        if (config_.enable_risk_management && !risk_manager_->Initialize()) {
            LogEvent("Failed to initialize risk manager", LogLevel::ERROR);
            return false;
        }

        // Initialize strategy manager
        if (!strategy_manager_->Initialize()) {
            LogEvent("Failed to initialize strategy manager", LogLevel::ERROR);
            return false;
        }

        // Set up event callbacks
        order_manager_->SetEventCallback([this](const OrderEvent& event) {
            HandleOrderEvent(event);
        });

        market_data_handler_->SetDataCallback([this](const MarketData& data) {
            OnMarketDataUpdate(data);
        });

        is_initialized_.store(true);
        LogEvent("Trading engine initialized successfully", LogLevel::INFO);
        return true;

    } catch (const std::exception& e) {
        LogEvent("Exception during initialization: " + std::string(e.what()), LogLevel::ERROR);
        return false;
    }
}

bool TradingEngine::Start() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    if (!is_initialized_.load()) {
        LogEvent("Cannot start engine - not initialized", LogLevel::ERROR);
        return false;
    }

    if (is_running_.load()) {
        LogEvent("Trading engine already running", LogLevel::WARNING);
        return true;
    }

    try {
        // Start core components
        if (!market_data_handler_->Start()) {
            LogEvent("Failed to start market data handler", LogLevel::ERROR);
            return false;
        }

        if (!order_manager_->Start()) {
            LogEvent("Failed to start order manager", LogLevel::ERROR);
            return false;
        }

        if (config_.enable_risk_management && !risk_manager_->Start()) {
            LogEvent("Failed to start risk manager", LogLevel::ERROR);
            return false;
        }

        if (!strategy_manager_->Start()) {
            LogEvent("Failed to start strategy manager", LogLevel::ERROR);
            return false;
        }

        // Start event processing thread
        event_processor_thread_ = std::thread(&TradingEngine::ProcessEvents, this);

        is_running_.store(true);
        start_time_ = std::chrono::high_resolution_clock::now();
        
        LogEvent("Trading engine started successfully", LogLevel::INFO);
        return true;

    } catch (const std::exception& e) {
        LogEvent("Exception during startup: " + std::string(e.what()), LogLevel::ERROR);
        return false;
    }
}

bool TradingEngine::Stop() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    if (!is_running_.load()) {
        return true;
    }

    try {
        is_running_.store(false);

        // Stop core components
        strategy_manager_->Stop();
        order_manager_->Stop();
        market_data_handler_->Stop();
        
        if (config_.enable_risk_management) {
            risk_manager_->Stop();
        }

        // Stop event processing
        event_condition_.notify_all();
        if (event_processor_thread_.joinable()) {
            event_processor_thread_.join();
        }

        LogEvent("Trading engine stopped successfully", LogLevel::INFO);
        return true;

    } catch (const std::exception& e) {
        LogEvent("Exception during shutdown: " + std::string(e.what()), LogLevel::ERROR);
        return false;
    }
}

OrderId TradingEngine::PlaceOrder(const OrderRequest& request) {
    if (!is_running_.load()) {
        throw std::runtime_error("Trading engine not running");
    }

    // Risk check
    if (config_.enable_risk_management) {
        auto risk_result = risk_manager_->ValidateOrder(request);
        if (!risk_result.approved) {
            LogEvent("Order rejected by risk manager: " + risk_result.reason, LogLevel::WARNING);
            return INVALID_ORDER_ID;
        }
    }

    // Place order through order manager
    auto order_id = order_manager_->PlaceOrder(request);
    
    if (order_id != INVALID_ORDER_ID) {
        LogEvent("Order placed successfully: " + std::to_string(order_id), LogLevel::INFO);
        UpdatePerformanceMetrics();
    }

    return order_id;
}

bool TradingEngine::CancelOrder(OrderId order_id) {
    if (!is_running_.load()) {
        return false;
    }

    bool result = order_manager_->CancelOrder(order_id);
    if (result) {
        LogEvent("Order cancelled: " + std::to_string(order_id), LogLevel::INFO);
    }
    
    return result;
}

void TradingEngine::OnMarketDataUpdate(const MarketData& data) {
    if (!is_running_.load()) {
        return;
    }

    // Update strategies with new market data
    strategy_manager_->OnMarketData(data);
    
    // Update risk manager
    if (config_.enable_risk_management) {
        risk_manager_->OnMarketData(data);
    }

    // Update performance metrics
    UpdatePerformanceMetrics();
}

void TradingEngine::ProcessEvents() {
    while (is_running_.load()) {
        std::unique_lock<std::mutex> lock(event_queue_mutex_);
        
        event_condition_.wait(lock, [this] {
            return !event_queue_.empty() || !is_running_.load();
        });

        while (!event_queue_.empty() && is_running_.load()) {
            auto event = std::move(event_queue_.front());
            event_queue_.pop();
            lock.unlock();
            
            try {
                event();
            } catch (const std::exception& e) {
                LogEvent("Exception in event processing: " + std::string(e.what()), LogLevel::ERROR);
            }
            
            lock.lock();
        }
    }
}

void TradingEngine::HandleOrderEvent(const OrderEvent& event) {
    if (order_callback_) {
        order_callback_(event);
    }
    
    LogEvent("Order event: " + event.ToString(), LogLevel::DEBUG);
}

void TradingEngine::UpdatePerformanceMetrics() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time_);
    
    performance_metrics_.uptime_ms = duration.count();
    performance_metrics_.total_orders = order_manager_->GetTotalOrderCount();
    performance_metrics_.active_orders = order_manager_->GetActiveOrderCount();
    performance_metrics_.total_pnl = GetTotalPnL();
}

void TradingEngine::LogEvent(const std::string& message, LogLevel level) {
    if (logger_) {
        logger_->Log(level, message);
    }
}

// Additional method implementations
bool TradingEngine::SubscribeToSymbol(const std::string& symbol) {
    if (!is_running_.load()) {
        return false;
    }
    return market_data_handler_->Subscribe(symbol);
}

bool TradingEngine::UnsubscribeFromSymbol(const std::string& symbol) {
    if (!is_running_.load()) {
        return false;
    }
    return market_data_handler_->Unsubscribe(symbol);
}

bool TradingEngine::ModifyOrder(OrderId order_id, const OrderModification& modification) {
    if (!is_running_.load()) {
        return false;
    }

    bool result = order_manager_->ModifyOrder(order_id, modification);
    if (result) {
        LogEvent("Order modified: " + std::to_string(order_id), LogLevel::INFO);
    }

    return result;
}

std::vector<Order> TradingEngine::GetActiveOrders() const {
    if (!is_running_.load()) {
        return {};
    }
    return order_manager_->GetActiveOrders();
}

std::vector<Order> TradingEngine::GetOrderHistory(const std::string& symbol) const {
    if (!is_running_.load()) {
        return {};
    }
    return order_manager_->GetOrderHistory(symbol);
}

Position TradingEngine::GetPosition(const std::string& symbol) const {
    if (!is_running_.load()) {
        return Position{};
    }
    return order_manager_->GetPosition(symbol);
}

std::vector<Position> TradingEngine::GetAllPositions() const {
    if (!is_running_.load()) {
        return {};
    }
    return order_manager_->GetAllPositions();
}

double TradingEngine::GetTotalPnL() const {
    if (!is_running_.load()) {
        return 0.0;
    }

    double total_pnl = 0.0;
    auto positions = order_manager_->GetAllPositions();
    for (const auto& position : positions) {
        total_pnl += position.realized_pnl + position.unrealized_pnl;
    }
    return total_pnl;
}

double TradingEngine::GetUnrealizedPnL() const {
    if (!is_running_.load()) {
        return 0.0;
    }

    double unrealized_pnl = 0.0;
    auto positions = order_manager_->GetAllPositions();
    for (const auto& position : positions) {
        unrealized_pnl += position.unrealized_pnl;
    }
    return unrealized_pnl;
}

bool TradingEngine::AddStrategy(std::unique_ptr<Strategy> strategy) {
    if (!is_running_.load()) {
        return false;
    }
    return strategy_manager_->AddStrategy(std::move(strategy));
}

bool TradingEngine::RemoveStrategy(const std::string& strategy_id) {
    if (!is_running_.load()) {
        return false;
    }
    return strategy_manager_->RemoveStrategy(strategy_id);
}

bool TradingEngine::StartStrategy(const std::string& strategy_id) {
    if (!is_running_.load()) {
        return false;
    }
    return strategy_manager_->StartStrategy(strategy_id);
}

bool TradingEngine::StopStrategy(const std::string& strategy_id) {
    if (!is_running_.load()) {
        return false;
    }
    return strategy_manager_->StopStrategy(strategy_id);
}

std::vector<StrategyInfo> TradingEngine::GetActiveStrategies() const {
    if (!is_running_.load()) {
        return {};
    }
    return strategy_manager_->GetActiveStrategies();
}

bool TradingEngine::SetRiskLimit(const std::string& symbol, const RiskLimit& limit) {
    if (!is_running_.load()) {
        return false;
    }
    return risk_manager_->SetRiskLimit(symbol, limit);
}

RiskStatus TradingEngine::GetRiskStatus() const {
    if (!is_running_.load()) {
        RiskStatus status;
        status.level = RiskStatusLevel::RED;
        status.message = "Trading engine not running";
        return status;
    }

    // Get risk status from risk manager
    RiskStatus status;
    status.level = RiskStatusLevel::GREEN;
    status.message = "All systems operational";
    status.emergency_stop_active = risk_manager_->IsEmergencyStopActive();
    status.risk_checks_enabled = risk_manager_->IsRiskChecksEnabled();
    status.last_updated = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    return status;
}

bool TradingEngine::EnableRiskManagement(bool enable) {
    if (!is_running_.load()) {
        return false;
    }
    return risk_manager_->EnableRiskChecks(enable);
}

PerformanceMetrics TradingEngine::GetPerformanceMetrics() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    return performance_metrics_;
}

void TradingEngine::ResetMetrics() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    performance_metrics_ = PerformanceMetrics{};
    start_time_ = std::chrono::high_resolution_clock::now();
}

void TradingEngine::ProcessEvents() {
    while (is_running_.load()) {
        std::unique_lock<std::mutex> lock(event_queue_mutex_);
        event_condition_.wait(lock, [this] {
            return !event_queue_.empty() || !is_running_.load();
        });

        while (!event_queue_.empty() && is_running_.load()) {
            auto event = event_queue_.front();
            event_queue_.pop();
            lock.unlock();

            try {
                event();
            } catch (const std::exception& e) {
                LogEvent("Event processing error: " + std::string(e.what()), LogLevel::ERROR);
            }

            lock.lock();
        }
    }
}

void TradingEngine::HandleOrderEvent(const OrderEvent& event) {
    if (order_callback_) {
        order_callback_(event);
    }

    // Update performance metrics
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    switch (event.type) {
        case OrderEventType::ORDER_FILLED:
            performance_metrics_.filled_orders++;
            break;
        case OrderEventType::ORDER_CANCELLED:
            performance_metrics_.cancelled_orders++;
            break;
        case OrderEventType::ORDER_REJECTED:
            performance_metrics_.rejected_orders++;
            break;
        default:
            break;
    }
}

void TradingEngine::HandlePositionEvent(const PositionEvent& event) {
    if (position_callback_) {
        position_callback_(event);
    }
}

void TradingEngine::HandleRiskEvent(const RiskEvent& event) {
    if (risk_callback_) {
        risk_callback_(event);
    }

    LogEvent("Risk event: " + event.ToString(), LogLevel::WARNING);
}

std::unique_ptr<TradingEngine> CreateTradingEngine(const EngineConfig& config) {
    return std::make_unique<TradingEngine>(config);
}

} // namespace zenflow
