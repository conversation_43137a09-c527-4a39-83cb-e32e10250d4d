#include "main_window.h"
#include <QApplication>
#include <QSettings>
#include <QMessageBox>
#include <QFileDialog>
#include <QDesktopServices>
#include <QUrl>
#include <QSplashScreen>
#include <QPixmap>
#include <QThread>

// Widget includes
#include "widgets/trading_widget.h"
#include "widgets/chart_widget.h"
#include "widgets/portfolio_widget.h"
#include "widgets/orderbook_widget.h"
#include "widgets/watchlist_widget.h"
#include "widgets/news_widget.h"
#include "widgets/log_widget.h"
#include "widgets/strategy_widget.h"
#include "widgets/risk_widget.h"
#include "widgets/performance_widget.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , trading_engine_(nullptr)
    , is_connected_(false)
    , is_market_open_(false)
    , account_balance_(0.0)
    , total_pnl_(0.0)
    , current_symbol_("AAPL")
{
    // Get trading engine from application
    auto* app = qobject_cast<ZenFlowApplication*>(QApplication::instance());
    if (app) {
        trading_engine_ = app->GetTradingEngine();
    }
    
    SetupUI();
    LoadSettings();
    SetupConnections();
    ApplyTheme();
    
    // Start timers
    status_update_timer_ = new QTimer(this);
    market_data_timer_ = new QTimer(this);
    performance_timer_ = new QTimer(this);
    
    connect(status_update_timer_, &QTimer::timeout, this, &MainWindow::UpdateStatus);
    connect(market_data_timer_, &QTimer::timeout, this, &MainWindow::UpdateMarketData);
    connect(performance_timer_, &QTimer::timeout, this, &MainWindow::UpdatePerformance);
    
    status_update_timer_->start(STATUS_UPDATE_INTERVAL);
    market_data_timer_->start(MARKET_DATA_INTERVAL);
    performance_timer_->start(PERFORMANCE_INTERVAL);
    
    // Set window properties
    setWindowTitle("ZenFlow Trading Platform");
    setWindowIcon(QIcon(":/icons/zenflow.png"));
    resize(1400, 900);
    
    // Center window on screen
    QRect screen = QApplication::desktop()->screenGeometry();
    move((screen.width() - width()) / 2, (screen.height() - height()) / 2);
}

MainWindow::~MainWindow() {
    SaveSettings();
}

void MainWindow::SetupUI() {
    CreateMenuBar();
    CreateToolBars();
    CreateStatusBar();
    CreateCentralWidget();
    CreateDockWidgets();
    SetupSystemTray();
}

void MainWindow::CreateMenuBar() {
    menu_bar_ = menuBar();
    
    // File menu
    file_menu_ = menu_bar_->addMenu("&File");
    file_menu_->addAction("&New Workspace", this, &MainWindow::OnNewWorkspace, QKeySequence::New);
    file_menu_->addAction("&Open Workspace", this, &MainWindow::OnOpenWorkspace, QKeySequence::Open);
    file_menu_->addAction("&Save Workspace", this, &MainWindow::OnSaveWorkspace, QKeySequence::Save);
    file_menu_->addSeparator();
    file_menu_->addAction("E&xit", this, &MainWindow::OnExit, QKeySequence::Quit);
    
    // Edit menu
    edit_menu_ = menu_bar_->addMenu("&Edit");
    edit_menu_->addAction("&Preferences", this, &MainWindow::OnPreferences, QKeySequence::Preferences);
    
    // View menu
    view_menu_ = menu_bar_->addMenu("&View");
    view_menu_->addAction("&Full Screen", this, &MainWindow::OnToggleFullScreen, QKeySequence::FullScreen);
    view_menu_->addAction("&Reset Layout", this, &MainWindow::OnResetLayout);
    view_menu_->addSeparator();
    
    // Trading menu
    trading_menu_ = menu_bar_->addMenu("&Trading");
    trading_menu_->addAction("Quick &Buy", this, &MainWindow::OnQuickBuy, QKeySequence("Ctrl+B"));
    trading_menu_->addAction("Quick &Sell", this, &MainWindow::OnQuickSell, QKeySequence("Ctrl+S"));
    trading_menu_->addSeparator();
    trading_menu_->addAction("Close All &Positions", this, &MainWindow::OnCloseAllPositions);
    trading_menu_->addAction("Cancel All &Orders", this, &MainWindow::OnCancelAllOrders);
    
    // Tools menu
    tools_menu_ = menu_bar_->addMenu("&Tools");
    tools_menu_->addAction("Strategy &Builder", this, &MainWindow::OnStrategyBuilder);
    tools_menu_->addAction("&Backtester", this, &MainWindow::OnBacktester);
    tools_menu_->addAction("&Risk Analyzer", this, &MainWindow::OnRiskAnalyzer);
    tools_menu_->addAction("&Market Scanner", this, &MainWindow::OnMarketScanner);
    
    // Help menu
    help_menu_ = menu_bar_->addMenu("&Help");
    help_menu_->addAction("&User Guide", this, &MainWindow::OnUserGuide);
    help_menu_->addAction("&Keyboard Shortcuts", this, &MainWindow::OnKeyboardShortcuts);
    help_menu_->addSeparator();
    help_menu_->addAction("Check for &Updates", this, &MainWindow::OnCheckUpdates);
    help_menu_->addAction("&About", this, &MainWindow::OnAbout);
}

void MainWindow::CreateToolBars() {
    // Main toolbar
    main_toolbar_ = addToolBar("Main");
    main_toolbar_->setObjectName("MainToolBar");
    main_toolbar_->addAction(QIcon(":/icons/new.png"), "New", this, &MainWindow::OnNewWorkspace);
    main_toolbar_->addAction(QIcon(":/icons/open.png"), "Open", this, &MainWindow::OnOpenWorkspace);
    main_toolbar_->addAction(QIcon(":/icons/save.png"), "Save", this, &MainWindow::OnSaveWorkspace);
    main_toolbar_->addSeparator();
    
    // Trading toolbar
    trading_toolbar_ = addToolBar("Trading");
    trading_toolbar_->setObjectName("TradingToolBar");
    trading_toolbar_->addAction(QIcon(":/icons/buy.png"), "Quick Buy", this, &MainWindow::OnQuickBuy);
    trading_toolbar_->addAction(QIcon(":/icons/sell.png"), "Quick Sell", this, &MainWindow::OnQuickSell);
    trading_toolbar_->addSeparator();
    trading_toolbar_->addAction(QIcon(":/icons/close_positions.png"), "Close All", this, &MainWindow::OnCloseAllPositions);
    trading_toolbar_->addAction(QIcon(":/icons/cancel_orders.png"), "Cancel All", this, &MainWindow::OnCancelAllOrders);
    
    // Chart toolbar
    chart_toolbar_ = addToolBar("Chart");
    chart_toolbar_->setObjectName("ChartToolBar");
    chart_toolbar_->addAction(QIcon(":/icons/zoom_in.png"), "Zoom In");
    chart_toolbar_->addAction(QIcon(":/icons/zoom_out.png"), "Zoom Out");
    chart_toolbar_->addAction(QIcon(":/icons/auto_scale.png"), "Auto Scale");
    chart_toolbar_->addSeparator();
    chart_toolbar_->addAction(QIcon(":/icons/indicators.png"), "Indicators");
    chart_toolbar_->addAction(QIcon(":/icons/drawing.png"), "Drawing Tools");
}

void MainWindow::CreateStatusBar() {
    status_bar_ = statusBar();
    
    // Connection status
    connection_status_label_ = new QLabel("Disconnected");
    connection_status_label_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
    status_bar_->addWidget(connection_status_label_);
    
    status_bar_->addWidget(new QLabel("|"));
    
    // Market status
    market_status_label_ = new QLabel("Market Closed");
    market_status_label_->setStyleSheet("QLabel { color: orange; }");
    status_bar_->addWidget(market_status_label_);
    
    status_bar_->addWidget(new QLabel("|"));
    
    // Account balance
    account_balance_label_ = new QLabel("Balance: $0.00");
    status_bar_->addWidget(account_balance_label_);
    
    status_bar_->addWidget(new QLabel("|"));
    
    // P&L
    pnl_label_ = new QLabel("P&L: $0.00");
    status_bar_->addWidget(pnl_label_);
    
    // Add stretch to push system info to the right
    status_bar_->addWidget(new QLabel(), 1);
    
    // CPU usage
    status_bar_->addWidget(new QLabel("CPU:"));
    cpu_usage_bar_ = new QProgressBar();
    cpu_usage_bar_->setMaximumWidth(100);
    cpu_usage_bar_->setMaximumHeight(16);
    cpu_usage_bar_->setRange(0, 100);
    status_bar_->addWidget(cpu_usage_bar_);
    
    // Memory usage
    status_bar_->addWidget(new QLabel("MEM:"));
    memory_usage_bar_ = new QProgressBar();
    memory_usage_bar_->setMaximumWidth(100);
    memory_usage_bar_->setMaximumHeight(16);
    memory_usage_bar_->setRange(0, 100);
    status_bar_->addWidget(memory_usage_bar_);
}

void MainWindow::CreateCentralWidget() {
    central_tabs_ = new QTabWidget(this);
    central_tabs_->setTabsClosable(true);
    central_tabs_->setMovable(true);
    
    // Create chart widget
    chart_widget_ = std::make_unique<ChartWidget>(this);
    central_tabs_->addTab(chart_widget_.get(), QIcon(":/icons/chart.png"), "Chart");
    
    setCentralWidget(central_tabs_);
}

void MainWindow::CreateDockWidgets() {
    // Watchlist dock
    watchlist_dock_ = new QDockWidget("Watchlist", this);
    watchlist_dock_->setObjectName("WatchlistDock");
    watchlist_widget_ = std::make_unique<WatchlistWidget>(this);
    watchlist_dock_->setWidget(watchlist_widget_.get());
    addDockWidget(Qt::LeftDockWidgetArea, watchlist_dock_);
    
    // Trading dock
    trading_dock_ = new QDockWidget("Trading", this);
    trading_dock_->setObjectName("TradingDock");
    trading_widget_ = std::make_unique<TradingWidget>(this);
    trading_dock_->setWidget(trading_widget_.get());
    addDockWidget(Qt::LeftDockWidgetArea, trading_dock_);
    
    // Portfolio dock
    portfolio_dock_ = new QDockWidget("Portfolio", this);
    portfolio_dock_->setObjectName("PortfolioDock");
    portfolio_widget_ = std::make_unique<PortfolioWidget>(this);
    portfolio_dock_->setWidget(portfolio_widget_.get());
    addDockWidget(Qt::RightDockWidgetArea, portfolio_dock_);
    
    // Orders dock
    orders_dock_ = new QDockWidget("Orders", this);
    orders_dock_->setObjectName("OrdersDock");
    orderbook_widget_ = std::make_unique<OrderBookWidget>(this);
    orders_dock_->setWidget(orderbook_widget_.get());
    addDockWidget(Qt::BottomDockWidgetArea, orders_dock_);
    
    // News dock
    news_dock_ = new QDockWidget("News", this);
    news_dock_->setObjectName("NewsDock");
    news_widget_ = std::make_unique<NewsWidget>(this);
    news_dock_->setWidget(news_widget_.get());
    addDockWidget(Qt::RightDockWidgetArea, news_dock_);
    
    // Log dock
    log_dock_ = new QDockWidget("Logs", this);
    log_dock_->setObjectName("LogDock");
    log_widget_ = std::make_unique<LogWidget>(this);
    log_dock_->setWidget(log_widget_.get());
    addDockWidget(Qt::BottomDockWidgetArea, log_dock_);
    
    // Strategy dock
    strategy_dock_ = new QDockWidget("Strategies", this);
    strategy_dock_->setObjectName("StrategyDock");
    strategy_widget_ = std::make_unique<StrategyWidget>(this);
    strategy_dock_->setWidget(strategy_widget_.get());
    addDockWidget(Qt::RightDockWidgetArea, strategy_dock_);
    
    // Risk dock
    risk_dock_ = new QDockWidget("Risk", this);
    risk_dock_->setObjectName("RiskDock");
    risk_widget_ = std::make_unique<RiskWidget>(this);
    risk_dock_->setWidget(risk_widget_.get());
    addDockWidget(Qt::RightDockWidgetArea, risk_dock_);
    
    // Performance dock
    performance_dock_ = new QDockWidget("Performance", this);
    performance_dock_->setObjectName("PerformanceDock");
    performance_widget_ = std::make_unique<PerformanceWidget>(this);
    performance_dock_->setWidget(performance_widget_.get());
    addDockWidget(Qt::BottomDockWidgetArea, performance_dock_);
    
    // Tabify some docks
    tabifyDockWidget(portfolio_dock_, news_dock_);
    tabifyDockWidget(news_dock_, strategy_dock_);
    tabifyDockWidget(strategy_dock_, risk_dock_);
    
    tabifyDockWidget(orders_dock_, log_dock_);
    tabifyDockWidget(log_dock_, performance_dock_);
    
    // Set initial dock visibility
    portfolio_dock_->raise();
    orders_dock_->raise();
}

void MainWindow::SetupSystemTray() {
    if (!QSystemTrayIcon::isSystemTrayAvailable()) {
        return;
    }
    
    tray_icon_ = new QSystemTrayIcon(this);
    tray_icon_->setIcon(QIcon(":/icons/zenflow_tray.png"));
    tray_icon_->setToolTip("ZenFlow Trading Platform");
    
    tray_menu_ = new QMenu(this);
    tray_menu_->addAction("Show/Hide", this, &MainWindow::OnShowHide);
    tray_menu_->addSeparator();
    tray_menu_->addAction("Exit", this, &MainWindow::OnExit);
    
    tray_icon_->setContextMenu(tray_menu_);
    
    connect(tray_icon_, &QSystemTrayIcon::activated, this, &MainWindow::OnTrayIconActivated);
    
    tray_icon_->show();
}

void MainWindow::LoadSettings() {
    QSettings settings;
    
    // Window geometry
    restoreGeometry(settings.value("geometry").toByteArray());
    restoreState(settings.value("windowState").toByteArray());
    
    // Application settings
    current_symbol_ = settings.value("currentSymbol", "AAPL").toString();
}

void MainWindow::SaveSettings() {
    QSettings settings;
    
    // Window geometry
    settings.setValue("geometry", saveGeometry());
    settings.setValue("windowState", saveState());
    
    // Application settings
    settings.setValue("currentSymbol", current_symbol_);
}

void MainWindow::SetupConnections() {
    // Connect trading widget signals
    if (trading_widget_) {
        connect(trading_widget_.get(), &TradingWidget::OrderPlaced, 
                this, &MainWindow::OnOrderPlaced);
    }
    
    // Connect watchlist signals
    if (watchlist_widget_) {
        connect(watchlist_widget_.get(), &WatchlistWidget::SymbolSelected,
                this, &MainWindow::OnSymbolSelected);
    }
}

void MainWindow::ApplyTheme() {
    // Apply theme from theme manager
    auto* app = qobject_cast<ZenFlowApplication*>(QApplication::instance());
    if (app && app->GetThemeManager()) {
        app->GetThemeManager()->ApplyTheme("dark");
    }
}

// Slot implementations
void MainWindow::OnNewWorkspace() {
    // Implementation for new workspace
    QMessageBox::information(this, "New Workspace", "New workspace functionality not yet implemented.");
}

void MainWindow::OnOpenWorkspace() {
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open Workspace", "", "Workspace Files (*.zfw)");
    if (!fileName.isEmpty()) {
        // Load workspace
        QMessageBox::information(this, "Open Workspace", "Workspace loading not yet implemented.");
    }
}

void MainWindow::OnSaveWorkspace() {
    QString fileName = QFileDialog::getSaveFileName(this,
        "Save Workspace", "", "Workspace Files (*.zfw)");
    if (!fileName.isEmpty()) {
        // Save workspace
        QMessageBox::information(this, "Save Workspace", "Workspace saving not yet implemented.");
    }
}

void MainWindow::OnExit() {
    close();
}

void MainWindow::OnPreferences() {
    QMessageBox::information(this, "Preferences", "Preferences dialog not yet implemented.");
}

void MainWindow::OnToggleFullScreen() {
    if (isFullScreen()) {
        showNormal();
    } else {
        showFullScreen();
    }
}

void MainWindow::OnResetLayout() {
    // Reset dock widget layout to default
    restoreState(QByteArray());
    CreateDockWidgets();
}

void MainWindow::OnToggleDockWidget() {
    // Toggle visibility of dock widgets
}

void MainWindow::OnQuickBuy() {
    if (trading_widget_) {
        trading_widget_->QuickBuy(current_symbol_);
    }
}

void MainWindow::OnQuickSell() {
    if (trading_widget_) {
        trading_widget_->QuickSell(current_symbol_);
    }
}

void MainWindow::OnCloseAllPositions() {
    if (trading_engine_) {
        auto positions = trading_engine_->GetAllPositions();
        for (const auto& position : positions) {
            if (!position.IsFlat()) {
                // Create close order
                zenflow::OrderRequest request;
                request.symbol = position.symbol;
                request.type = zenflow::OrderType::MARKET;
                request.side = position.IsLong() ? zenflow::OrderSide::SELL : zenflow::OrderSide::BUY;
                request.quantity = std::abs(position.quantity);
                request.strategy_id = "manual_close";

                trading_engine_->PlaceOrder(request);
            }
        }
    }
}

void MainWindow::OnCancelAllOrders() {
    if (trading_engine_) {
        auto orders = trading_engine_->GetActiveOrders();
        for (const auto& order : orders) {
            trading_engine_->CancelOrder(order.id);
        }
    }
}

void MainWindow::OnStrategyBuilder() {
    QMessageBox::information(this, "Strategy Builder", "Strategy builder not yet implemented.");
}

void MainWindow::OnBacktester() {
    QMessageBox::information(this, "Backtester", "Backtester not yet implemented.");
}

void MainWindow::OnRiskAnalyzer() {
    QMessageBox::information(this, "Risk Analyzer", "Risk analyzer not yet implemented.");
}

void MainWindow::OnMarketScanner() {
    QMessageBox::information(this, "Market Scanner", "Market scanner not yet implemented.");
}

void MainWindow::OnAbout() {
    QMessageBox::about(this, "About ZenFlow Trading",
        "<h2>ZenFlow Trading Platform</h2>"
        "<p>Version 1.0.0</p>"
        "<p>High-performance algorithmic trading platform</p>"
        "<p>Built with Qt6 and C++</p>"
        "<p>Copyright © 2024 ZenFlow</p>");
}

void MainWindow::OnUserGuide() {
    QDesktopServices::openUrl(QUrl("https://docs.zenflow.trade"));
}

void MainWindow::OnKeyboardShortcuts() {
    QMessageBox::information(this, "Keyboard Shortcuts",
        "<h3>Keyboard Shortcuts</h3>"
        "<p><b>Ctrl+N</b> - New Workspace</p>"
        "<p><b>Ctrl+O</b> - Open Workspace</p>"
        "<p><b>Ctrl+S</b> - Save Workspace</p>"
        "<p><b>Ctrl+B</b> - Quick Buy</p>"
        "<p><b>Ctrl+Shift+S</b> - Quick Sell</p>"
        "<p><b>F11</b> - Toggle Full Screen</p>"
        "<p><b>Ctrl+Q</b> - Exit</p>");
}

void MainWindow::OnCheckUpdates() {
    QMessageBox::information(this, "Check Updates", "Update checking not yet implemented.");
}

void MainWindow::OnTrayIconActivated(QSystemTrayIcon::ActivationReason reason) {
    if (reason == QSystemTrayIcon::DoubleClick) {
        OnShowHide();
    }
}

void MainWindow::OnShowHide() {
    if (isVisible()) {
        hide();
    } else {
        show();
        raise();
        activateWindow();
    }
}

void MainWindow::UpdateStatus() {
    // Update connection status
    if (trading_engine_ && trading_engine_->IsRunning()) {
        if (!is_connected_) {
            is_connected_ = true;
            connection_status_label_->setText("Connected");
            connection_status_label_->setStyleSheet("QLabel { color: green; font-weight: bold; }");
        }
    } else {
        if (is_connected_) {
            is_connected_ = false;
            connection_status_label_->setText("Disconnected");
            connection_status_label_->setStyleSheet("QLabel { color: red; font-weight: bold; }");
        }
    }

    // Update market status (simplified)
    QTime now = QTime::currentTime();
    bool market_open = (now >= QTime(9, 30) && now <= QTime(16, 0));
    if (market_open != is_market_open_) {
        is_market_open_ = market_open;
        market_status_label_->setText(market_open ? "Market Open" : "Market Closed");
        market_status_label_->setStyleSheet(market_open ?
            "QLabel { color: green; }" : "QLabel { color: orange; }");
    }

    // Update account balance and P&L
    if (trading_engine_) {
        double pnl = trading_engine_->GetTotalPnL();
        if (pnl != total_pnl_) {
            total_pnl_ = pnl;
            pnl_label_->setText(QString("P&L: $%1").arg(pnl, 0, 'f', 2));
            pnl_label_->setStyleSheet(pnl >= 0 ?
                "QLabel { color: green; }" : "QLabel { color: red; }");
        }
    }

    // Update system usage (simplified)
    cpu_usage_bar_->setValue(qrand() % 100);
    memory_usage_bar_->setValue(qrand() % 100);
}

void MainWindow::UpdateMarketData() {
    // Update market data in widgets
    if (chart_widget_) {
        chart_widget_->UpdateData();
    }

    if (watchlist_widget_) {
        watchlist_widget_->UpdatePrices();
    }
}

void MainWindow::UpdatePerformance() {
    if (performance_widget_) {
        performance_widget_->UpdateMetrics();
    }
}

void MainWindow::OnOrderPlaced(const QString& symbol, double quantity, double price) {
    statusBar()->showMessage(QString("Order placed: %1 %2 @ %3")
        .arg(symbol).arg(quantity).arg(price), 3000);
}

void MainWindow::OnSymbolSelected(const QString& symbol) {
    current_symbol_ = symbol;
    if (chart_widget_) {
        chart_widget_->SetSymbol(symbol);
    }
}

void MainWindow::closeEvent(QCloseEvent *event) {
    if (tray_icon_ && tray_icon_->isVisible()) {
        hide();
        event->ignore();
        tray_icon_->showMessage("ZenFlow Trading",
            "Application minimized to tray", QSystemTrayIcon::Information, 2000);
    } else {
        SaveSettings();
        event->accept();
    }
}

void MainWindow::changeEvent(QEvent *event) {
    if (event->type() == QEvent::WindowStateChange) {
        if (isMinimized() && tray_icon_ && tray_icon_->isVisible()) {
            hide();
            event->ignore();
            return;
        }
    }
    QMainWindow::changeEvent(event);
}

#include "main_window.moc"
