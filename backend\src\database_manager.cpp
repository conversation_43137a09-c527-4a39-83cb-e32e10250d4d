#include "database_manager.h"
#include "../../core/utils/logger.h"
#include <sstream>
#include <chrono>
#include <random>

DatabaseManager::DatabaseManager(const DatabaseConfig& config)
    : config_(config), is_connected_(false) {
}

DatabaseManager::~DatabaseManager() {
    Disconnect();
}

bool DatabaseManager::Initialize() {
    try {
        LOG_INFO("Initializing database connection to " + config_.host + ":" + std::to_string(config_.port));
        
        if (!Connect()) {
            LOG_ERROR("Failed to connect to database");
            return false;
        }
        
        if (!TestConnection()) {
            LOG_ERROR("Database connection test failed");
            return false;
        }
        
        InitializeTables();
        
        LOG_INFO("Database manager initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Database initialization error: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseManager::Connect() {
    try {
        std::string conn_str = BuildConnectionString();
        connection_ = std::make_unique<pqxx::connection>(conn_str);
        
        if (connection_->is_open()) {
            is_connected_ = true;
            LOG_INFO("Connected to database: " + config_.database);
            return true;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Database connection error: " + std::string(e.what()));
        return false;
    }
}

void DatabaseManager::Disconnect() {
    if (connection_ && connection_->is_open()) {
        connection_->close();
        is_connected_ = false;
        LOG_INFO("Disconnected from database");
    }
}

bool DatabaseManager::IsConnected() const {
    return is_connected_ && connection_ && connection_->is_open();
}

std::string DatabaseManager::BuildConnectionString() const {
    std::ostringstream oss;
    oss << "host=" << config_.host
        << " port=" << config_.port
        << " dbname=" << config_.database
        << " user=" << config_.username
        << " password=" << config_.password;
    
    if (config_.enable_ssl) {
        oss << " sslmode=require";
        if (!config_.ssl_cert_file.empty()) {
            oss << " sslcert=" << config_.ssl_cert_file;
        }
        if (!config_.ssl_key_file.empty()) {
            oss << " sslkey=" << config_.ssl_key_file;
        }
    } else {
        oss << " sslmode=disable";
    }
    
    oss << " connect_timeout=" << config_.connection_timeout;
    
    return oss.str();
}

bool DatabaseManager::TestConnection() {
    try {
        if (!IsConnected()) {
            return false;
        }
        
        pqxx::work txn(*connection_);
        pqxx::result result = txn.exec("SELECT 1");
        txn.commit();
        
        return !result.empty();
        
    } catch (const std::exception& e) {
        LOG_ERROR("Database connection test failed: " + std::string(e.what()));
        return false;
    }
}

void DatabaseManager::InitializeTables() {
    // Tables are created via migration scripts
    // This method can be used for any additional initialization
    LOG_INFO("Database tables initialized");
}

bool DatabaseManager::CreateUser(const User& user) {
    try {
        if (!IsConnected()) {
            return false;
        }
        
        pqxx::work txn(*connection_);
        
        std::string query = R"(
            INSERT INTO users (id, username, email, password_hash, is_admin, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        )";
        
        int64_t now = GetCurrentTimestamp();
        
        txn.exec_params(query,
            user.id,
            user.username,
            user.email,
            user.password_hash,
            user.is_admin,
            user.is_active,
            now,
            now
        );
        
        txn.commit();
        LOG_INFO("User created: " + user.username);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Create user error: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseManager::GetUser(const std::string& user_id, User& user) {
    try {
        if (!IsConnected()) {
            return false;
        }
        
        pqxx::work txn(*connection_);
        
        std::string query = R"(
            SELECT id, username, email, password_hash, is_admin, is_active, created_at, updated_at
            FROM users WHERE id = $1
        )";
        
        pqxx::result result = txn.exec_params(query, user_id);
        
        if (result.empty()) {
            return false;
        }
        
        user = ResultToUser(result[0]);
        txn.commit();
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Get user error: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseManager::GetUserByUsername(const std::string& username, User& user) {
    try {
        if (!IsConnected()) {
            return false;
        }
        
        pqxx::work txn(*connection_);
        
        std::string query = R"(
            SELECT id, username, email, password_hash, is_admin, is_active, created_at, updated_at
            FROM users WHERE username = $1
        )";
        
        pqxx::result result = txn.exec_params(query, username);
        
        if (result.empty()) {
            return false;
        }
        
        user = ResultToUser(result[0]);
        txn.commit();
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Get user by username error: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseManager::SaveOrder(const std::string& order_json) {
    try {
        if (!IsConnected()) {
            return false;
        }
        
        pqxx::work txn(*connection_);
        
        // Parse JSON to extract key fields
        Json::Value order;
        Json::CharReaderBuilder builder;
        std::string errors;
        std::istringstream stream(order_json);
        
        if (!Json::parseFromStream(builder, stream, &order, &errors)) {
            LOG_ERROR("Failed to parse order JSON: " + errors);
            return false;
        }
        
        std::string query = R"(
            INSERT INTO orders (id, account_id, instrument_id, order_type, side, quantity, 
                              price, stop_price, time_in_force, status, created_at, order_data)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        )";
        
        txn.exec_params(query,
            order.get("id", "").asString(),
            order.get("account_id", "").asString(),
            order.get("instrument_id", "").asString(),
            order.get("type", "MARKET").asString(),
            order.get("side", "BUY").asString(),
            order.get("quantity", 0.0).asDouble(),
            order.get("price", 0.0).asDouble(),
            order.get("stop_price", 0.0).asDouble(),
            order.get("time_in_force", "DAY").asString(),
            order.get("status", "PENDING").asString(),
            GetCurrentTimestamp(),
            order_json
        );
        
        txn.commit();
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Save order error: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseManager::SaveMarketData(const std::string& symbol, const std::string& market_data_json) {
    try {
        if (!IsConnected()) {
            return false;
        }
        
        pqxx::work txn(*connection_);
        
        // Parse JSON to extract key fields
        Json::Value data;
        Json::CharReaderBuilder builder;
        std::string errors;
        std::istringstream stream(market_data_json);
        
        if (!Json::parseFromStream(builder, stream, &data, &errors)) {
            LOG_ERROR("Failed to parse market data JSON: " + errors);
            return false;
        }
        
        // For TimescaleDB, we need to find or create the instrument first
        std::string instrument_query = R"(
            INSERT INTO instruments (symbol, name, asset_class, exchange, currency)
            VALUES ($1, $2, 'STOCK', 'NYSE', 'USD')
            ON CONFLICT (symbol, exchange) DO NOTHING
            RETURNING id
        )";
        
        pqxx::result instrument_result = txn.exec_params(instrument_query, symbol, symbol);
        
        // Get instrument ID
        std::string get_instrument_query = "SELECT id FROM instruments WHERE symbol = $1 LIMIT 1";
        pqxx::result id_result = txn.exec_params(get_instrument_query, symbol);
        
        if (id_result.empty()) {
            LOG_ERROR("Failed to get instrument ID for symbol: " + symbol);
            return false;
        }
        
        std::string instrument_id = id_result[0][0].as<std::string>();
        
        std::string query = R"(
            INSERT INTO market_data (time, instrument_id, price, volume, bid, ask, data_type)
            VALUES (NOW(), $1, $2, $3, $4, $5, $6)
        )";
        
        txn.exec_params(query,
            instrument_id,
            data.get("price", 0.0).asDouble(),
            data.get("volume", 0.0).asDouble(),
            data.get("bid", 0.0).asDouble(),
            data.get("ask", 0.0).asDouble(),
            data.get("type", "TICK").asString()
        );
        
        txn.commit();
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Save market data error: " + std::string(e.what()));
        return false;
    }
}

bool DatabaseManager::ExecuteQuery(const std::string& query) {
    try {
        if (!IsConnected()) {
            return false;
        }
        
        pqxx::work txn(*connection_);
        txn.exec(query);
        txn.commit();
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Execute query error: " + std::string(e.what()));
        return false;
    }
}

User DatabaseManager::ResultToUser(const pqxx::row& row) {
    User user;
    user.id = row["id"].as<std::string>();
    user.username = row["username"].as<std::string>();
    user.email = row["email"].as<std::string>();
    user.password_hash = row["password_hash"].as<std::string>();
    user.is_admin = row["is_admin"].as<bool>();
    user.is_active = row["is_active"].as<bool>();
    user.created_at = row["created_at"].as<int64_t>();
    user.updated_at = row["updated_at"].as<int64_t>();
    return user;
}

std::string DatabaseManager::GenerateUUID() {
    // Simple UUID generation - in production, use proper UUID library
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);
    
    std::string uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";
    for (char& c : uuid) {
        if (c == 'x') {
            c = "0123456789abcdef"[dis(gen)];
        } else if (c == 'y') {
            c = "89ab"[dis(gen) % 4];
        }
    }
    return uuid;
}

int64_t DatabaseManager::GetCurrentTimestamp() {
    return std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}
