#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>
#include <functional>

#include <websocketpp/config/asio_no_tls.hpp>
#include <websocketpp/server.hpp>
#include <json/json.h>

#include "../../core/engine/trading_engine.h"

struct WebSocketConfig {
    std::string host = "0.0.0.0";
    int port = 8081;
    int max_connections = 1000;
    int ping_interval = 30; // seconds
    int message_queue_size = 1000;
    bool enable_compression = true;
    bool enable_rate_limiting = true;
    int rate_limit_messages_per_second = 10;
};

struct ClientSession {
    std::string session_id;
    std::string user_id;
    websocketpp::connection_hdl connection;
    std::unordered_set<std::string> subscriptions;
    int64_t connected_at;
    int64_t last_activity;
    bool authenticated = false;
    
    // Rate limiting
    std::queue<int64_t> message_timestamps;
    int messages_sent = 0;
};

enum class MessageType {
    AUTHENTICATE,
    SUBSCRIBE,
    UNSUBSCRIBE,
    MARKET_DATA,
    ORDER_UPDATE,
    POSITION_UPDATE,
    STRATEGY_UPDATE,
    RISK_ALERT,
    SYSTEM_STATUS,
    ERROR,
    PING,
    PONG
};

struct WebSocketMessage {
    MessageType type;
    std::string channel;
    Json::Value data;
    std::string client_id;
    int64_t timestamp;
    
    std::string ToJson() const;
    static WebSocketMessage FromJson(const std::string& json_str);
};

class WebSocketServer {
public:
    explicit WebSocketServer(const WebSocketConfig& config, zenflow::TradingEngine* trading_engine);
    ~WebSocketServer();

    bool Initialize();
    bool Start();
    void Stop();
    bool IsRunning() const { return is_running_.load(); }

    // Client management
    void BroadcastMessage(const WebSocketMessage& message);
    void SendToClient(const std::string& client_id, const WebSocketMessage& message);
    void SendToSubscribers(const std::string& channel, const WebSocketMessage& message);
    
    // Subscription management
    bool Subscribe(const std::string& client_id, const std::string& channel);
    bool Unsubscribe(const std::string& client_id, const std::string& channel);
    std::unordered_set<std::string> GetSubscriptions(const std::string& client_id);
    
    // Statistics
    size_t GetConnectedClients() const;
    size_t GetTotalSubscriptions() const;
    Json::Value GetServerStats() const;

private:
    using Server = websocketpp::server<websocketpp::config::asio>;
    using ConnectionPtr = Server::connection_ptr;
    using MessagePtr = Server::message_ptr;

    WebSocketConfig config_;
    zenflow::TradingEngine* trading_engine_;
    
    std::unique_ptr<Server> server_;
    std::thread server_thread_;
    std::atomic<bool> is_running_{false};
    
    // Client management
    std::unordered_map<std::string, ClientSession> clients_;
    std::unordered_map<websocketpp::connection_hdl, std::string, 
                      std::owner_less<websocketpp::connection_hdl>> connection_to_client_;
    mutable std::mutex clients_mutex_;
    
    // Subscription management
    std::unordered_map<std::string, std::unordered_set<std::string>> channel_subscribers_;
    mutable std::mutex subscriptions_mutex_;
    
    // Message queue for broadcasting
    std::queue<WebSocketMessage> message_queue_;
    std::mutex message_queue_mutex_;
    std::condition_variable message_queue_condition_;
    std::thread message_processor_thread_;
    
    // Ping/Pong management
    std::thread ping_thread_;
    
    // Event handlers
    void OnOpen(websocketpp::connection_hdl hdl);
    void OnClose(websocketpp::connection_hdl hdl);
    void OnMessage(websocketpp::connection_hdl hdl, MessagePtr msg);
    void OnPing(websocketpp::connection_hdl hdl, std::string payload);
    void OnPong(websocketpp::connection_hdl hdl, std::string payload);
    
    // Message processing
    void ProcessMessage(const std::string& client_id, const WebSocketMessage& message);
    void ProcessMessageQueue();
    void HandleAuthenticate(const std::string& client_id, const Json::Value& data);
    void HandleSubscribe(const std::string& client_id, const Json::Value& data);
    void HandleUnsubscribe(const std::string& client_id, const Json::Value& data);
    void HandlePing(const std::string& client_id, const Json::Value& data);
    
    // Trading engine callbacks
    void OnMarketDataUpdate(const zenflow::MarketData& data);
    void OnOrderEvent(const zenflow::OrderEvent& event);
    void OnPositionEvent(const zenflow::PositionEvent& event);
    void OnRiskEvent(const zenflow::RiskEvent& event);
    
    // Utility methods
    std::string GenerateClientId();
    bool AuthenticateClient(const std::string& token, std::string& user_id);
    bool CheckRateLimit(const std::string& client_id);
    void SendError(const std::string& client_id, const std::string& error_message);
    void CleanupClient(const std::string& client_id);
    void SendPing();
    void UpdateClientActivity(const std::string& client_id);
    
    // Channel definitions
    static const std::string MARKET_DATA_CHANNEL;
    static const std::string ORDER_UPDATES_CHANNEL;
    static const std::string POSITION_UPDATES_CHANNEL;
    static const std::string STRATEGY_UPDATES_CHANNEL;
    static const std::string RISK_ALERTS_CHANNEL;
    static const std::string SYSTEM_STATUS_CHANNEL;
};

// Rate limiter for WebSocket messages
class WebSocketRateLimiter {
public:
    explicit WebSocketRateLimiter(int messages_per_second = 10, int burst_capacity = 20);
    
    bool AllowMessage(const std::string& client_id);
    void Reset(const std::string& client_id);
    void CleanupClient(const std::string& client_id);
    
private:
    int messages_per_second_;
    int burst_capacity_;
    
    struct ClientLimitData {
        std::queue<int64_t> message_timestamps;
        int current_count = 0;
        int64_t last_reset = 0;
    };
    
    std::unordered_map<std::string, ClientLimitData> client_limits_;
    std::mutex limits_mutex_;
    
    void UpdateCounts(const std::string& client_id);
    int64_t GetCurrentTimestamp() const;
};

// WebSocket message builder helper
class MessageBuilder {
public:
    static WebSocketMessage CreateMarketDataMessage(const zenflow::MarketData& data);
    static WebSocketMessage CreateOrderUpdateMessage(const zenflow::OrderEvent& event);
    static WebSocketMessage CreatePositionUpdateMessage(const zenflow::PositionEvent& event);
    static WebSocketMessage CreateRiskAlertMessage(const zenflow::RiskEvent& event);
    static WebSocketMessage CreateSystemStatusMessage(const Json::Value& status);
    static WebSocketMessage CreateErrorMessage(const std::string& error_message);
    static WebSocketMessage CreatePingMessage();
    static WebSocketMessage CreatePongMessage();
    
private:
    static int64_t GetCurrentTimestamp();
};

#endif // WEBSOCKET_SERVER_H
