#pragma once

#include <memory>
#include <unordered_map>
#include <vector>
#include <string>
#include <atomic>
#include <mutex>
#include <thread>
#include <functional>
#include <chrono>

#include "../market_data/market_data_handler.h"
#include "../orders/order_manager.h"

namespace zenflow {

// Forward declarations
class Strategy;
class TradingEngine;

enum class StrategyState {
    STOPPED,
    STARTING,
    RUNNING,
    STOPPING,
    ERROR
};

struct StrategyInfo {
    std::string id;
    std::string name;
    std::string description;
    StrategyState state;
    double pnl;
    int64_t start_time;
    int64_t last_update;
    std::unordered_map<std::string, double> parameters;
    std::unordered_map<std::string, std::string> metadata;
};

struct StrategyPerformance {
    std::string strategy_id;
    double total_pnl;
    double unrealized_pnl;
    double realized_pnl;
    int total_trades;
    int winning_trades;
    int losing_trades;
    double win_rate;
    double sharpe_ratio;
    double max_drawdown;
    double volatility;
    int64_t last_updated;
};

// Base strategy interface
class Strategy {
public:
    Strategy(const std::string& id, const std::string& name);
    virtual ~Strategy() = default;

    // Lifecycle methods
    virtual bool Initialize() = 0;
    virtual bool Start() = 0;
    virtual bool Stop() = 0;
    virtual void Reset() = 0;

    // Market data callbacks
    virtual void OnMarketData(const MarketData& data) = 0;
    virtual void OnOrderEvent(const OrderEvent& event) = 0;
    virtual void OnPositionUpdate(const Position& position) = 0;

    // Strategy information
    const std::string& GetId() const { return id_; }
    const std::string& GetName() const { return name_; }
    StrategyState GetState() const { return state_.load(); }
    
    // Parameter management
    virtual void SetParameter(const std::string& name, double value);
    virtual double GetParameter(const std::string& name) const;
    virtual std::unordered_map<std::string, double> GetAllParameters() const;

    // Performance tracking
    StrategyPerformance GetPerformance() const;
    void UpdatePerformance();

    // Trading engine access
    void SetTradingEngine(TradingEngine* engine) { trading_engine_ = engine; }

protected:
    // Helper methods for derived strategies
    OrderId PlaceOrder(const OrderRequest& request);
    bool CancelOrder(OrderId order_id);
    Position GetPosition(const std::string& symbol) const;
    void LogMessage(const std::string& message, LogLevel level = LogLevel::INFO);

    // State management
    void SetState(StrategyState state) { state_.store(state); }
    bool IsRunning() const { return state_.load() == StrategyState::RUNNING; }

private:
    std::string id_;
    std::string name_;
    std::atomic<StrategyState> state_{StrategyState::STOPPED};
    
    // Parameters
    std::unordered_map<std::string, double> parameters_;
    mutable std::mutex parameters_mutex_;
    
    // Performance tracking
    StrategyPerformance performance_;
    mutable std::mutex performance_mutex_;
    
    // Trading engine reference
    TradingEngine* trading_engine_ = nullptr;
    
    // Timing
    std::chrono::high_resolution_clock::time_point start_time_;
    std::chrono::high_resolution_clock::time_point last_update_time_;
};

// Strategy manager class
class StrategyManager {
public:
    StrategyManager();
    ~StrategyManager();

    // Lifecycle management
    bool Initialize();
    bool Start();
    bool Stop();
    bool IsRunning() const { return is_running_.load(); }

    // Strategy management
    bool AddStrategy(std::unique_ptr<Strategy> strategy);
    bool RemoveStrategy(const std::string& strategy_id);
    bool StartStrategy(const std::string& strategy_id);
    bool StopStrategy(const std::string& strategy_id);
    bool RestartStrategy(const std::string& strategy_id);

    // Strategy information
    std::vector<StrategyInfo> GetActiveStrategies() const;
    std::vector<StrategyInfo> GetAllStrategies() const;
    StrategyInfo GetStrategyInfo(const std::string& strategy_id) const;
    bool HasStrategy(const std::string& strategy_id) const;

    // Market data distribution
    void OnMarketData(const MarketData& data);
    void OnOrderEvent(const OrderEvent& event);
    void OnPositionUpdate(const Position& position);

    // Performance monitoring
    std::vector<StrategyPerformance> GetAllPerformance() const;
    StrategyPerformance GetStrategyPerformance(const std::string& strategy_id) const;
    double GetTotalPnL() const;

    // Configuration
    void SetTradingEngine(TradingEngine* engine);
    void SetMaxStrategies(size_t max_strategies) { max_strategies_ = max_strategies; }
    size_t GetMaxStrategies() const { return max_strategies_; }

    // Callbacks
    void SetStrategyEventCallback(std::function<void(const std::string&, StrategyState)> callback);

private:
    // Strategy storage
    std::unordered_map<std::string, std::unique_ptr<Strategy>> strategies_;
    mutable std::mutex strategies_mutex_;

    // State management
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_initialized_{false};
    
    // Configuration
    size_t max_strategies_ = 100;
    TradingEngine* trading_engine_ = nullptr;

    // Event callback
    std::function<void(const std::string&, StrategyState)> strategy_event_callback_;
    std::mutex callback_mutex_;

    // Performance tracking
    mutable std::mutex performance_mutex_;
    std::chrono::high_resolution_clock::time_point last_performance_update_;

    // Helper methods
    void UpdateAllPerformance();
    void NotifyStrategyStateChange(const std::string& strategy_id, StrategyState state);
    bool ValidateStrategy(const Strategy* strategy) const;
};

} // namespace zenflow
