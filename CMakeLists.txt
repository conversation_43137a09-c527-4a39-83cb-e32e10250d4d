cmake_minimum_required(VERSION 3.16)
project(ZenFlowTrading VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build options
option(BUILD_CORE "Build core trading engine" ON)
option(BUILD_DESKTOP "Build Qt desktop application" ON)
option(BUILD_BACKEND "Build C++ backend server" ON)
option(BUILD_TESTS "Build test suite" OFF)
option(BUILD_EXAMPLES "Build example applications" OFF)
option(BUILD_DOCS "Build documentation" OFF)
option(ENABLE_COVERAGE "Enable code coverage" OFF)
option(ENABLE_SANITIZERS "Enable sanitizers" OFF)

# Set default build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release CACHE STRING "Build type" FORCE)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4 /permissive-)
    add_compile_definitions(_WIN32_WINNT=0x0601)
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(/O2 /DNDEBUG)
    else()
        add_compile_options(/Od /Zi)
    endif()
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(-O3 -DNDEBUG)
    else()
        add_compile_options(-g -O0)
    endif()
    
    # Enable sanitizers in debug mode
    if(ENABLE_SANITIZERS AND CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-fsanitize=address -fsanitize=undefined)
        add_link_options(-fsanitize=address -fsanitize=undefined)
    endif()
endif()

# Enable code coverage
if(ENABLE_COVERAGE AND CMAKE_BUILD_TYPE STREQUAL "Debug")
    if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
        add_compile_options(--coverage)
        add_link_options(--coverage)
    endif()
endif()

# Find required packages
find_package(Threads REQUIRED)

# Core trading engine
if(BUILD_CORE)
    add_subdirectory(core)
endif()

# Qt Desktop application
if(BUILD_DESKTOP)
    find_package(Qt6 REQUIRED COMPONENTS Core Widgets Charts Network WebSockets)
    if(Qt6_FOUND)
        add_subdirectory(desktop)
    else()
        message(WARNING "Qt6 not found, skipping desktop application build")
        set(BUILD_DESKTOP OFF)
    endif()
endif()

# C++ Backend server
if(BUILD_BACKEND)
    # Find backend dependencies
    find_package(PkgConfig REQUIRED)
    
    # Check for Crow framework
    find_path(CROW_INCLUDE_DIR crow.h PATHS /usr/include /usr/local/include)
    if(NOT CROW_INCLUDE_DIR)
        message(WARNING "Crow framework not found, skipping backend build")
        set(BUILD_BACKEND OFF)
    else()
        add_subdirectory(backend)
    endif()
endif()

# Testing
if(BUILD_TESTS)
    enable_testing()
    find_package(GTest QUIET)
    if(GTest_FOUND)
        add_subdirectory(tests)
    else()
        message(WARNING "Google Test not found, skipping tests")
        set(BUILD_TESTS OFF)
    endif()
endif()

# Examples
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# Documentation
if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        # Configure Doxygen
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    else()
        message(WARNING "Doxygen not found, skipping documentation")
        set(BUILD_DOCS OFF)
    endif()
endif()

# Installation
include(GNUInstallDirs)

# Install core library
if(BUILD_CORE)
    install(TARGETS zenflow_core
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    )
endif()

# Install desktop application
if(BUILD_DESKTOP)
    install(TARGETS zenflow_desktop
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    )
    
    # Install desktop files on Linux
    if(UNIX AND NOT APPLE)
        install(FILES desktop/zenflow-trading.desktop
            DESTINATION ${CMAKE_INSTALL_DATAROOTDIR}/applications
        )
        install(FILES desktop/icons/zenflow-trading.png
            DESTINATION ${CMAKE_INSTALL_DATAROOTDIR}/icons/hicolor/256x256/apps
        )
    endif()
endif()

# Install backend server
if(BUILD_BACKEND)
    install(TARGETS zenflow_backend
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    )
    
    # Install configuration files
    install(DIRECTORY config/
        DESTINATION ${CMAKE_INSTALL_SYSCONFDIR}/zenflow
        FILES_MATCHING PATTERN "*.json" PATTERN "*.conf"
    )
    
    # Install systemd service file on Linux
    if(UNIX AND NOT APPLE)
        install(FILES scripts/zenflow-backend.service
            DESTINATION ${CMAKE_INSTALL_DATAROOTDIR}/systemd/system
        )
    endif()
endif()

# Install documentation
if(BUILD_DOCS)
    install(DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs/html/
        DESTINATION ${CMAKE_INSTALL_DOCDIR}
    )
endif()

# CPack configuration for packaging
include(CPack)
set(CPACK_PACKAGE_NAME "ZenFlow Trading Platform")
set(CPACK_PACKAGE_VENDOR "ZenFlow")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "High-Performance Algorithmic Trading Platform")
set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})
set(CPACK_PACKAGE_CONTACT "<EMAIL>")
set(CPACK_PACKAGE_HOMEPAGE_URL "https://zenflow.trade")

# Platform-specific packaging
if(WIN32)
    set(CPACK_GENERATOR "NSIS;ZIP")
    set(CPACK_NSIS_DISPLAY_NAME "ZenFlow Trading Platform")
    set(CPACK_NSIS_PACKAGE_NAME "ZenFlow Trading")
    set(CPACK_NSIS_CONTACT "<EMAIL>")
    set(CPACK_NSIS_URL_INFO_ABOUT "https://zenflow.trade")
elseif(APPLE)
    set(CPACK_GENERATOR "DragNDrop;TGZ")
    set(CPACK_DMG_VOLUME_NAME "ZenFlow Trading")
elseif(UNIX)
    set(CPACK_GENERATOR "DEB;RPM;TGZ")
    set(CPACK_DEBIAN_PACKAGE_MAINTAINER "Hector Ta <<EMAIL>>")
    set(CPACK_DEBIAN_PACKAGE_SECTION "finance")
    set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")
    set(CPACK_RPM_PACKAGE_GROUP "Applications/Finance")
    set(CPACK_RPM_PACKAGE_LICENSE "MIT")
endif()

# Custom targets
add_custom_target(format
    COMMAND find ${CMAKE_SOURCE_DIR}/core ${CMAKE_SOURCE_DIR}/desktop ${CMAKE_SOURCE_DIR}/backend 
            -name "*.cpp" -o -name "*.h" | xargs clang-format -i
    COMMENT "Formatting source code"
)

add_custom_target(lint
    COMMAND find ${CMAKE_SOURCE_DIR}/core ${CMAKE_SOURCE_DIR}/desktop ${CMAKE_SOURCE_DIR}/backend 
            -name "*.cpp" -o -name "*.h" | xargs cppcheck --enable=all --std=c++17
    COMMENT "Running static analysis"
)

# Print configuration summary
message(STATUS "")
message(STATUS "ZenFlow Trading Platform Configuration Summary:")
message(STATUS "==============================================")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
message(STATUS "Components:")
message(STATUS "  Core engine: ${BUILD_CORE}")
message(STATUS "  Desktop app: ${BUILD_DESKTOP}")
message(STATUS "  Backend server: ${BUILD_BACKEND}")
message(STATUS "  Tests: ${BUILD_TESTS}")
message(STATUS "  Examples: ${BUILD_EXAMPLES}")
message(STATUS "  Documentation: ${BUILD_DOCS}")
message(STATUS "")
message(STATUS "Options:")
message(STATUS "  Code coverage: ${ENABLE_COVERAGE}")
message(STATUS "  Sanitizers: ${ENABLE_SANITIZERS}")
message(STATUS "")

if(BUILD_DESKTOP AND Qt6_FOUND)
    message(STATUS "Qt6 version: ${Qt6_VERSION}")
endif()

if(BUILD_BACKEND AND CROW_INCLUDE_DIR)
    message(STATUS "Crow framework: Found")
endif()

message(STATUS "==============================================")
message(STATUS "")
