#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTextEdit>
#include <QComboBox>
#include <QPushButton>
#include <QCheckBox>
#include "../../../core/utils/logger.h"

class LogWidget : public QWidget {
    Q_OBJECT

public:
    explicit LogWidget(QWidget* parent = nullptr);
    void AddLogEntry(const QString& message, zenflow::LogLevel level);

private slots:
    void OnClearLogs();
    void OnLevelFilterChanged();
    void OnAutoScrollToggled(bool enabled);

private:
    void SetupUI();
    void ApplyFilter();
    QString FormatLogEntry(const QString& message, zenflow::LogLevel level);
    
    QVBoxLayout* main_layout_;
    QHBoxLayout* controls_layout_;
    QComboBox* level_filter_;
    QPushButton* clear_button_;
    QCheckBox* auto_scroll_checkbox_;
    QTextEdit* log_text_;
    
    zenflow::LogLevel current_filter_level_;
    bool auto_scroll_enabled_;
};

// Simple widgets for other components
class NewsWidget : public QWidget {
    Q_OBJECT
public:
    explicit NewsWidget(QWidget* parent = nullptr) : QWidget(parent) {}
};

class StrategyWidget : public QWidget {
    Q_OBJECT
public:
    explicit StrategyWidget(QWidget* parent = nullptr) : QWidget(parent) {}
};

class RiskWidget : public QWidget {
    Q_OBJECT
public:
    explicit RiskWidget(QWidget* parent = nullptr) : QWidget(parent) {}
};

class PerformanceWidget : public QWidget {
    Q_OBJECT
public:
    explicit PerformanceWidget(QWidget* parent = nullptr) : QWidget(parent) {}
    void UpdateMetrics() {}
};

#endif
