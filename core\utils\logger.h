#pragma once

#include <string>
#include <fstream>
#include <memory>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <sstream>
#include <iomanip>

namespace zenflow {

enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARNING = 3,
    ERROR = 4,
    CRITICAL = 5
};

struct LogEntry {
    LogLevel level;
    std::string message;
    std::string file;
    int line;
    std::string function;
    std::chrono::system_clock::time_point timestamp;
    std::thread::id thread_id;
    
    std::string ToString() const;
};

class LogSink {
public:
    virtual ~LogSink() = default;
    virtual void Write(const LogEntry& entry) = 0;
    virtual void Flush() = 0;
};

class ConsoleSink : public LogSink {
public:
    ConsoleSink(bool use_colors = true);
    void Write(const LogEntry& entry) override;
    void Flush() override;

private:
    bool use_colors_;
    std::mutex console_mutex_;
    
    std::string GetColorCode(LogLevel level) const;
    std::string GetResetCode() const;
};

class FileSink : public LogSink {
public:
    explicit FileSink(const std::string& filename, bool append = true);
    ~FileSink();
    
    void Write(const LogEntry& entry) override;
    void Flush() override;
    
    bool IsOpen() const { return file_.is_open(); }
    void Rotate(size_t max_size_mb = 100);

private:
    std::ofstream file_;
    std::string filename_;
    std::mutex file_mutex_;
    size_t max_file_size_;
    
    void RotateFile();
    size_t GetFileSize() const;
};

class Logger {
public:
    explicit Logger(const std::string& name = "ZenFlow", LogLevel min_level = LogLevel::INFO);
    ~Logger();

    // Logging methods
    void Log(LogLevel level, const std::string& message, 
             const std::string& file = "", int line = 0, const std::string& function = "");
    
    void Trace(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Debug(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Info(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Warning(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Error(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");
    void Critical(const std::string& message, const std::string& file = "", int line = 0, const std::string& function = "");

    // Configuration
    void SetLevel(LogLevel level) { min_level_.store(level); }
    LogLevel GetLevel() const { return min_level_.load(); }
    
    void AddSink(std::unique_ptr<LogSink> sink);
    void RemoveAllSinks();
    
    void SetAsyncMode(bool async);
    bool IsAsyncMode() const { return async_mode_.load(); }
    
    void Flush();
    void Shutdown();

    // Static methods for global logger
    static Logger& GetGlobalLogger();
    static void SetGlobalLogger(std::unique_ptr<Logger> logger);

private:
    std::string name_;
    std::atomic<LogLevel> min_level_;
    std::vector<std::unique_ptr<LogSink>> sinks_;
    std::mutex sinks_mutex_;
    
    // Async logging
    std::atomic<bool> async_mode_{false};
    std::atomic<bool> shutdown_{false};
    std::queue<LogEntry> log_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::thread async_thread_;
    
    void ProcessAsyncLogs();
    void WriteToSinks(const LogEntry& entry);
    bool ShouldLog(LogLevel level) const;
    
    static std::unique_ptr<Logger> global_logger_;
    static std::mutex global_logger_mutex_;
};

// Convenience macros
#define LOG_TRACE(msg) zenflow::Logger::GetGlobalLogger().Trace(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_DEBUG(msg) zenflow::Logger::GetGlobalLogger().Debug(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_INFO(msg) zenflow::Logger::GetGlobalLogger().Info(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_WARNING(msg) zenflow::Logger::GetGlobalLogger().Warning(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_ERROR(msg) zenflow::Logger::GetGlobalLogger().Error(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_CRITICAL(msg) zenflow::Logger::GetGlobalLogger().Critical(msg, __FILE__, __LINE__, __FUNCTION__)

// Utility functions
std::string LogLevelToString(LogLevel level);
LogLevel StringToLogLevel(const std::string& level_str);
std::string FormatTimestamp(const std::chrono::system_clock::time_point& timestamp);

} // namespace zenflow
